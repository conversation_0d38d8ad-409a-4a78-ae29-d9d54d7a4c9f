/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppProvider: typeof import('./src/components/common/app-provider.vue')['default']
    BetterScroll: typeof import('./src/components/custom/better-scroll.vue')['default']
    ButtonIcon: typeof import('./src/components/custom/button-icon.vue')['default']
    CountTo: typeof import('./src/components/custom/count-to.vue')['default']
    DarkModeContainer: typeof import('./src/components/common/dark-mode-container.vue')['default']
    ExceptionBase: typeof import('./src/components/common/exception-base.vue')['default']
    FullScreen: typeof import('./src/components/common/full-screen.vue')['default']
    LangSwitch: typeof import('./src/components/common/lang-switch.vue')['default']
    LookForward: typeof import('./src/components/custom/look-forward.vue')['default']
    LpxTimetable: typeof import('./src/components/lpx-timetable/lpx-timetable.vue')['default']
    MarkdownEditor: typeof import('./src/components/common/MarkdownEditor.vue')['default']
    MenuToggler: typeof import('./src/components/common/menu-toggler.vue')['default']
    PinToggler: typeof import('./src/components/common/pin-toggler.vue')['default']
    ReloadButton: typeof import('./src/components/common/reload-button.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SoybeanAvatar: typeof import('./src/components/custom/soybean-avatar.vue')['default']
    SvgIcon: typeof import('./src/components/custom/svg-icon.vue')['default']
    SystemLogo: typeof import('./src/components/common/system-logo.vue')['default']
    TableColumnSetting: typeof import('./src/components/advanced/table-column-setting.vue')['default']
    TableHeaderOperation: typeof import('./src/components/advanced/table-header-operation.vue')['default']
    ThemeSchemaSwitch: typeof import('./src/components/common/theme-schema-switch.vue')['default']
    WaveBg: typeof import('./src/components/custom/wave-bg.vue')['default']
  }
}
