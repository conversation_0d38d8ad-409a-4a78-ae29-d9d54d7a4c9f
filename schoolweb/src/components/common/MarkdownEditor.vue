<template>
  <div class="markdown-editor">
    <div class="editor-toolbar">
      <NButtonGroup size="small">
        <NButton @click="insertText('**', '**')" title="粗体">
          <template #icon>
            <div class="i-material-symbols:format-bold"></div>
          </template>
        </NButton>
        <NButton @click="insertText('*', '*')" title="斜体">
          <template #icon>
            <div class="i-material-symbols:format-italic"></div>
          </template>
        </NButton>
        <NButton @click="insertText('`', '`')" title="行内代码">
          <template #icon>
            <div class="i-material-symbols:code"></div>
          </template>
        </NButton>
        <NButton @click="insertText('[链接文字](', ')')" title="链接">
          <template #icon>
            <div class="i-material-symbols:link"></div>
          </template>
        </NButton>
        <NButton @click="insertText('![图片描述](', ')')" title="图片">
          <template #icon>
            <div class="i-material-symbols:image"></div>
          </template>
        </NButton>
        <NButton @click="insertHeading" title="标题">
          <template #icon>
            <div class="i-material-symbols:title"></div>
          </template>
        </NButton>
        <NButton @click="insertList" title="列表">
          <template #icon>
            <div class="i-material-symbols:format-list-bulleted"></div>
          </template>
        </NButton>
        <NButton @click="insertCodeBlock" title="代码块">
          <template #icon>
            <div class="i-material-symbols:code-blocks"></div>
          </template>
        </NButton>
        <NButton @click="insertMath" title="数学公式">
          <template #icon>
            <div class="i-material-symbols:function"></div>
          </template>
        </NButton>
      </NButtonGroup>
      
      <div class="flex-1"></div>
      
      <NButtonGroup size="small">
        <NButton @click="showPreview = !showPreview" :type="showPreview ? 'primary' : 'default'">
          <template #icon>
            <div class="i-material-symbols:preview"></div>
          </template>
          {{ showPreview ? '编辑' : '预览' }}
        </NButton>
      </NButtonGroup>
    </div>
    
    <div class="editor-content" :class="{ 'split-view': showPreview }">
      <div v-show="!showPreview || showPreview" class="editor-input">
        <NInput
          ref="textareaRef"
          v-model:value="content"
          type="textarea"
          :placeholder="placeholder"
          :autosize="autosize"
          @input="handleInput"
          @keydown="handleKeydown"
        />
      </div>
      
      <div v-if="showPreview" class="editor-preview">
        <div class="preview-content markdown-body" v-html="renderedMarkdown"></div>
      </div>
    </div>
    
    <div class="editor-footer">
      <div class="text-12px text-gray-500">
        支持 Markdown 语法 | 字符数: {{ content.length }}
        <span v-if="maxLength"> / {{ maxLength }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { marked } from 'marked';
import hljs from 'highlight.js';
import katex from 'katex';

interface Props {
  modelValue: string;
  placeholder?: string;
  maxLength?: number;
  autosize?: { minRows?: number; maxRows?: number };
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入内容，支持 Markdown 语法',
  autosize: () => ({ minRows: 6, maxRows: 20 })
});

const emit = defineEmits<Emits>();

const content = ref(props.modelValue);
const showPreview = ref(false);
const textareaRef = ref();

// 配置 marked
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value;
      } catch (err) {
        console.error('Highlight error:', err);
      }
    }
    return hljs.highlightAuto(code).value;
  },
  breaks: true,
  gfm: true
});

// 渲染 Markdown（包含数学公式支持）
const renderedMarkdown = computed(() => {
  if (!content.value) return '<p class="text-gray-400">暂无内容</p>';
  try {
    // 先渲染Markdown
    let html = marked(content.value);

    // 然后处理数学公式
    // 处理块级数学公式 $$...$$
    html = html.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
      try {
        return katex.renderToString(formula.trim(), {
          displayMode: true,
          throwOnError: false
        });
      } catch (error) {
        console.error('KaTeX block error:', error);
        return match; // 保留原始文本
      }
    });

    // 处理行内数学公式 $...$
    html = html.replace(/\$([^$\n]+?)\$/g, (match, formula) => {
      try {
        return katex.renderToString(formula.trim(), {
          displayMode: false,
          throwOnError: false
        });
      } catch (error) {
        console.error('KaTeX inline error:', error);
        return match; // 保留原始文本
      }
    });

    return html;
  } catch (error) {
    console.error('Markdown render error:', error);
    return '<p class="text-red-500">Markdown 渲染错误</p>';
  }
});

// 监听内容变化
watch(content, (newValue) => {
  emit('update:modelValue', newValue);
});

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== content.value) {
    content.value = newValue;
  }
});

// 处理输入
function handleInput() {
  if (props.maxLength && content.value.length > props.maxLength) {
    content.value = content.value.slice(0, props.maxLength);
  }
}

// 处理键盘事件
function handleKeydown(event: KeyboardEvent) {
  // Tab 键插入空格
  if (event.key === 'Tab') {
    event.preventDefault();
    insertText('  ', '');
  }
}

// 获取光标位置
function getCursorPosition() {
  const textarea = textareaRef.value?.textareaElRef;
  if (!textarea) return { start: 0, end: 0 };
  return {
    start: textarea.selectionStart,
    end: textarea.selectionEnd
  };
}

// 设置光标位置
function setCursorPosition(start: number, end?: number) {
  nextTick(() => {
    const textarea = textareaRef.value?.textareaElRef;
    if (!textarea) return;
    textarea.focus();
    textarea.setSelectionRange(start, end || start);
  });
}

// 插入文本
function insertText(before: string, after: string = '') {
  const { start, end } = getCursorPosition();
  const selectedText = content.value.slice(start, end);
  const newText = before + selectedText + after;
  
  content.value = content.value.slice(0, start) + newText + content.value.slice(end);
  
  // 设置新的光标位置
  const newCursorPos = start + before.length + selectedText.length;
  setCursorPosition(newCursorPos);
}

// 插入标题
function insertHeading() {
  const { start } = getCursorPosition();
  const lineStart = content.value.lastIndexOf('\n', start - 1) + 1;
  const lineEnd = content.value.indexOf('\n', start);
  const currentLine = content.value.slice(lineStart, lineEnd === -1 ? undefined : lineEnd);
  
  if (currentLine.startsWith('### ')) {
    // 移除标题
    content.value = content.value.slice(0, lineStart) + currentLine.slice(4) + content.value.slice(lineEnd === -1 ? undefined : lineEnd);
  } else if (currentLine.startsWith('## ')) {
    // 三级标题
    content.value = content.value.slice(0, lineStart) + '### ' + currentLine.slice(3) + content.value.slice(lineEnd === -1 ? undefined : lineEnd);
  } else if (currentLine.startsWith('# ')) {
    // 二级标题
    content.value = content.value.slice(0, lineStart) + '## ' + currentLine.slice(2) + content.value.slice(lineEnd === -1 ? undefined : lineEnd);
  } else {
    // 一级标题
    content.value = content.value.slice(0, lineStart) + '# ' + currentLine + content.value.slice(lineEnd === -1 ? undefined : lineEnd);
  }
}

// 插入列表
function insertList() {
  const { start } = getCursorPosition();
  const lineStart = content.value.lastIndexOf('\n', start - 1) + 1;
  const beforeCursor = content.value.slice(0, start);
  const afterCursor = content.value.slice(start);
  
  if (beforeCursor.endsWith('\n') || start === 0) {
    content.value = beforeCursor + '- ' + afterCursor;
    setCursorPosition(start + 2);
  } else {
    content.value = beforeCursor + '\n- ' + afterCursor;
    setCursorPosition(start + 3);
  }
}

// 插入代码块
function insertCodeBlock() {
  const { start, end } = getCursorPosition();
  const selectedText = content.value.slice(start, end);
  const beforeCursor = content.value.slice(0, start);
  const afterCursor = content.value.slice(end);

  const codeBlock = '\n```\n' + selectedText + '\n```\n';
  content.value = beforeCursor + codeBlock + afterCursor;

  if (selectedText) {
    setCursorPosition(start + codeBlock.length);
  } else {
    setCursorPosition(start + 5); // 光标放在代码块内
  }
}

// 插入数学公式
function insertMath() {
  const { start, end } = getCursorPosition();
  const selectedText = content.value.slice(start, end);

  if (selectedText) {
    // 如果有选中文本，包装为行内公式
    insertText('$', '$');
  } else {
    // 没有选中文本，插入块级公式
    const beforeCursor = content.value.slice(0, start);
    const afterCursor = content.value.slice(end);
    const mathBlock = '\n$$\n\n$$\n';
    content.value = beforeCursor + mathBlock + afterCursor;
    setCursorPosition(start + 4); // 光标放在公式内
  }
}
</script>

<style scoped>
/* 导入 KaTeX 样式 */
@import 'katex/dist/katex.min.css';

.markdown-editor {
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #fafafa;
  border-bottom: 1px solid #e0e0e6;
  gap: 8px;
}

.editor-content {
  display: flex;
  min-height: 200px;
}

.editor-content.split-view {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.editor-input {
  flex: 1;
}

.editor-input :deep(.n-input) {
  border: none;
  border-radius: 0;
}

.editor-input :deep(.n-input__border),
.editor-input :deep(.n-input__state-border) {
  display: none;
}

.editor-preview {
  flex: 1;
  border-left: 1px solid #e0e0e6;
  background-color: #fafafa;
  overflow-y: auto;
}

.preview-content {
  padding: 12px;
  height: 100%;
}

.editor-footer {
  padding: 8px 12px;
  background-color: #fafafa;
  border-top: 1px solid #e0e0e6;
  text-align: right;
}

/* Markdown 样式 */
.markdown-body {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-body h1 { font-size: 24px; }
.markdown-body h2 { font-size: 20px; }
.markdown-body h3 { font-size: 16px; }

.markdown-body p {
  margin: 8px 0;
}

.markdown-body ul,
.markdown-body ol {
  margin: 8px 0;
  padding-left: 20px;
}

.markdown-body li {
  margin: 4px 0;
}

.markdown-body code {
  background-color: #f6f8fa;
  border-radius: 3px;
  font-size: 85%;
  margin: 0;
  padding: 2px 4px;
}

.markdown-body pre {
  background-color: #f6f8fa;
  border-radius: 6px;
  font-size: 85%;
  line-height: 1.45;
  overflow: auto;
  padding: 16px;
  margin: 8px 0;
}

.markdown-body pre code {
  background-color: transparent;
  border: 0;
  display: inline;
  line-height: inherit;
  margin: 0;
  max-width: auto;
  overflow: visible;
  padding: 0;
  word-wrap: normal;
}

.markdown-body blockquote {
  border-left: 4px solid #dfe2e5;
  margin: 8px 0;
  padding: 0 16px;
  color: #6a737d;
}

.markdown-body a {
  color: #0366d6;
  text-decoration: none;
}

.markdown-body a:hover {
  text-decoration: underline;
}

.markdown-body strong {
  font-weight: 600;
}

.markdown-body em {
  font-style: italic;
}

/* 数学公式样式 */
.markdown-body .katex {
  font-size: 1.1em;
}

.markdown-body .katex-display {
  margin: 16px 0;
  text-align: center;
}

.markdown-body .katex-display .katex {
  display: inline-block;
  white-space: nowrap;
}
</style>
