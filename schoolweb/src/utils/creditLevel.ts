/**
 * 信用等级工具函数
 */

// 信用等级定义
export interface CreditLevelInfo {
  code: string;
  name: string;
  minScore: number;
  maxScore: number;
  color: string;
  description: string;
}

// 信用等级映射
export const CREDIT_LEVELS: Record<string, CreditLevelInfo> = {
  S: {
    code: 'S',
    name: '优秀',
    minScore: 90,
    maxScore: 100,
    color: 'success',
    description: '信用优秀，值得信赖'
  },
  A: {
    code: 'A',
    name: '良好',
    minScore: 80,
    maxScore: 89,
    color: 'info',
    description: '信用良好，表现不错'
  },
  B: {
    code: 'B',
    name: '一般',
    minScore: 70,
    maxScore: 79,
    color: 'warning',
    description: '信用一般，有待提升'
  },
  C: {
    code: 'C',
    name: '较差',
    minScore: 60,
    maxScore: 69,
    color: 'error',
    description: '信用较差，需要改善'
  },
  D: {
    code: 'D',
    name: '很差',
    minScore: 0,
    maxScore: 59,
    color: 'error',
    description: '信用很差，急需改善'
  }
};

/**
 * 根据分数获取信用等级
 */
export function getCreditLevelByScore(score: number): CreditLevelInfo {
  for (const level of Object.values(CREDIT_LEVELS)) {
    if (score >= level.minScore && score <= level.maxScore) {
      return level;
    }
  }
  return CREDIT_LEVELS.D; // 默认返回最低等级
}

/**
 * 根据等级代码获取信用等级信息
 */
export function getCreditLevelByCode(code: string): CreditLevelInfo {
  return CREDIT_LEVELS[code] || CREDIT_LEVELS.B;
}

/**
 * 获取信用等级显示文本
 */
export function getCreditLevelText(code: string): string {
  const level = getCreditLevelByCode(code);
  return `${level.code}级 - ${level.name}`;
}

/**
 * 获取信用等级颜色
 */
export function getCreditLevelColor(code: string): string {
  const level = getCreditLevelByCode(code);
  return level.color;
}

/**
 * 获取信用等级描述
 */
export function getCreditLevelDescription(code: string): string {
  const level = getCreditLevelByCode(code);
  return level.description;
}

/**
 * 获取所有信用等级列表
 */
export function getAllCreditLevels(): CreditLevelInfo[] {
  return Object.values(CREDIT_LEVELS);
}

/**
 * 验证信用分数是否有效
 */
export function isValidCreditScore(score: number): boolean {
  return score >= 0 && score <= 100;
}

/**
 * 格式化信用分数显示
 */
export function formatCreditScore(score: number): string {
  if (!isValidCreditScore(score)) {
    return '--';
  }
  return score.toString();
}

/**
 * 获取分数变化的颜色
 */
export function getScoreChangeColor(oldScore: number, newScore: number): string {
  if (newScore > oldScore) {
    return 'success'; // 绿色，分数提升
  } else if (newScore < oldScore) {
    return 'error'; // 红色，分数下降
  } else {
    return 'default'; // 默认颜色，分数不变
  }
}

/**
 * 获取分数变化的图标
 */
export function getScoreChangeIcon(oldScore: number, newScore: number): string {
  if (newScore > oldScore) {
    return 'i-material-symbols:trending-up'; // 上升箭头
  } else if (newScore < oldScore) {
    return 'i-material-symbols:trending-down'; // 下降箭头
  } else {
    return 'i-material-symbols:trending-flat'; // 平行箭头
  }
}
