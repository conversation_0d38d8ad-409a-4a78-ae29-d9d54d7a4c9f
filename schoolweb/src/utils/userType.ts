/**
 * 用户类型枚举
 */
export enum UserType {
  STUDENT = 1,
  TEACHER = 2,
  ADMIN = 3
}

/**
 * 用户类型映射
 */
export const USER_TYPE_MAP = {
  [UserType.STUDENT]: '学生',
  [UserType.TEACHER]: '教师',
  [UserType.ADMIN]: '管理员'
} as const;

/**
 * 用户类型标签颜色映射
 */
export const USER_TYPE_COLOR_MAP = {
  [UserType.STUDENT]: 'info',
  [UserType.TEACHER]: 'success',
  [UserType.ADMIN]: 'warning'
} as const;

/**
 * 获取用户类型显示文本
 */
export function getUserTypeText(userType?: number): string {
  if (!userType || !USER_TYPE_MAP[userType as UserType]) {
    return '';
  }
  return USER_TYPE_MAP[userType as UserType];
}

/**
 * 获取用户类型标签颜色
 */
export function getUserTypeColor(userType?: number): string {
  if (!userType || !USER_TYPE_COLOR_MAP[userType as UserType]) {
    return 'default';
  }
  return USER_TYPE_COLOR_MAP[userType as UserType];
}
