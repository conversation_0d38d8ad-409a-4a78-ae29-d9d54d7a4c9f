import type { CustomRoute } from '@elegant-router/types';
import { layouts, views } from '../elegant/imports';
import { getRoutePath, transformElegantRoutesToVueRoutes } from '../elegant/transform';

export const ROOT_ROUTE: CustomRoute = {
  name: 'root',
  path: '/',
  redirect: (to) => {
    // 动态重定向：根据用户角色决定跳转页面
    const token = localStorage.getItem('token');
    if (!token) {
      return '/login';
    }

    // 尝试从多个来源获取用户信息
    let userRoles: string[] = [];

    // 1. 尝试从 localStorage 获取
    const userInfoStr = localStorage.getItem('userInfo');
    if (userInfoStr) {
      try {
        const userInfo = JSON.parse(userInfoStr);
        userRoles = userInfo.roles || [];
      } catch (error) {
        console.error('解析 localStorage 用户信息失败:', error);
      }
    }

    // 2. 如果 localStorage 没有，尝试从 sessionStorage 获取
    if (userRoles.length === 0) {
      const sessionUserInfoStr = sessionStorage.getItem('userInfo');
      if (sessionUserInfoStr) {
        try {
          const userInfo = JSON.parse(sessionUserInfoStr);
          userRoles = userInfo.roles || [];
        } catch (error) {
          console.error('解析 sessionStorage 用户信息失败:', error);
        }
      }
    }

    // 根据角色决定跳转页面
    if (userRoles.includes('superAdmin')) {
      return '/home';
    }

    // 默认跳转到技能服务页面
    return '/skill';
  },
  meta: {
    title: 'root',
    constant: true
  }
};

const NOT_FOUND_ROUTE: CustomRoute = {
  name: 'not-found',
  path: '/:pathMatch(.*)*',
  component: 'layout.blank$view.404',
  meta: {
    title: 'not-found',
    constant: true
  }
};

/** builtin routes, it must be constant and setup in vue-router */
const builtinRoutes: CustomRoute[] = [ROOT_ROUTE, NOT_FOUND_ROUTE];

/** create builtin vue routes */
export function createBuiltinVueRoutes() {
  return transformElegantRoutesToVueRoutes(builtinRoutes, layouts, views);
}
