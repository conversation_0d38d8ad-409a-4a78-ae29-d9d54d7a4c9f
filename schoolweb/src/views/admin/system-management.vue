<template>
  <div class="system-management">
    <n-card title="系统管理" :bordered="false" size="small" class="rounded-8px shadow-sm">
      <div class="space-y-6">
        <!-- 系统备份 -->
        <n-card title="系统备份" :bordered="false" size="small" class="rounded-8px">
          <div class="space-y-4">
            <div class="text-gray-600 text-sm">
              系统备份功能可以帮助您备份数据库和上传文件，确保数据安全。数据库导出为SQL文件，文件打包为ZIP压缩包。
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <!-- 完整系统备份 -->
              <n-card :bordered="false" class="bg-blue-50 hover:bg-blue-100 transition-colors">
                <div class="text-center space-y-3">
                  <div class="text-blue-600">
                    <n-icon size="32">
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M12 2C13.1 2 14 2.9 14 4V8C14 9.1 13.1 10 12 10S10 9.1 10 8V4C10 2.9 10.9 2 12 2M21 9V7L19 5L17 7V9C16.4 9 16 9.4 16 10V16C16 16.6 16.4 17 17 17H21C21.6 17 22 16.6 22 16V10C22 9.4 21.6 9 21 9M20 15H18V11H20V15M7.88 3.39L6.6 1.86L2 5.71L3.29 7.24L7.88 3.39M22 12L20.46 13.03L21.71 14.56L23.25 13.53L22 12M8 10.5V12.5H4V10.5H8M13 16.25V18.25H11V16.25H13M15 12L16.25 13.25L14.75 14.75L13.5 13.5L15 12Z"/>
                      </svg>
                    </n-icon>
                  </div>
                  <div>
                    <div class="font-medium text-gray-800">完整系统备份</div>
                    <div class="text-sm text-gray-600">包含数据库和文件</div>
                  </div>
                  <n-button 
                    type="primary" 
                    size="small" 
                    :loading="backupLoading.system"
                    @click="handleSystemBackup"
                  >
                    开始备份
                  </n-button>
                </div>
              </n-card>

              <!-- 数据库备份 -->
              <n-card :bordered="false" class="bg-green-50 hover:bg-green-100 transition-colors">
                <div class="text-center space-y-3">
                  <div class="text-green-600">
                    <n-icon size="32">
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M12 3C16.42 3 20 4.79 20 7C20 9.21 16.42 11 12 11S4 9.21 4 7C4 4.79 7.58 3 12 3M4 9V12C4 14.21 7.58 16 12 16S20 14.21 20 12V9C20 11.21 16.42 13 12 13S4 11.21 4 9M4 14V17C4 19.21 7.58 21 12 21S20 19.21 20 17V14C20 16.21 16.42 18 12 18S4 16.21 4 14Z"/>
                      </svg>
                    </n-icon>
                  </div>
                  <div>
                    <div class="font-medium text-gray-800">数据库备份</div>
                    <div class="text-sm text-gray-600">仅导出数据库</div>
                  </div>
                  <n-button 
                    type="success" 
                    size="small" 
                    :loading="backupLoading.database"
                    @click="handleDatabaseBackup"
                  >
                    导出数据库
                  </n-button>
                </div>
              </n-card>

              <!-- 文件备份 -->
              <n-card :bordered="false" class="bg-orange-50 hover:bg-orange-100 transition-colors">
                <div class="text-center space-y-3">
                  <div class="text-orange-600">
                    <n-icon size="32">
                      <svg viewBox="0 0 24 24">
                        <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                      </svg>
                    </n-icon>
                  </div>
                  <div>
                    <div class="font-medium text-gray-800">文件备份</div>
                    <div class="text-sm text-gray-600">仅打包上传文件</div>
                  </div>
                  <n-button 
                    type="warning" 
                    size="small" 
                    :loading="backupLoading.files"
                    @click="handleFilesBackup"
                  >
                    打包文件
                  </n-button>
                </div>
              </n-card>
            </div>
          </div>
        </n-card>

        <!-- 备份历史 -->
        <n-card title="备份历史" :bordered="false" size="small" class="rounded-8px">
          <div class="space-y-4">
            <div v-if="backupHistory.length === 0" class="text-center text-gray-500 py-8">
              暂无备份记录
            </div>
            <div v-else class="space-y-2">
              <div 
                v-for="backup in backupHistory" 
                :key="backup.id"
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div class="flex items-center space-x-3">
                  <n-icon size="20" :color="getBackupTypeColor(backup.type)">
                    <svg viewBox="0 0 24 24">
                      <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                  </n-icon>
                  <div>
                    <div class="font-medium">{{ backup.name }}</div>
                    <div class="text-sm text-gray-600">{{ backup.createTime }}</div>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <n-tag :type="getBackupTypeTag(backup.type)" size="small">
                    {{ backup.type }}
                  </n-tag>
                  <n-button 
                    size="small" 
                    type="primary" 
                    text
                    @click="downloadBackup(backup.path)"
                  >
                    下载
                  </n-button>
                </div>
              </div>
            </div>
          </div>
        </n-card>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useMessage } from 'naive-ui';
import { createSystemBackup, exportDatabase, packUploadFiles } from '@/service/api';

const message = useMessage();

// 备份加载状态
const backupLoading = ref({
  system: false,
  database: false,
  files: false
});

// 备份历史记录
const backupHistory = ref<Array<{
  id: string;
  name: string;
  type: string;
  path: string;
  createTime: string;
}>>([]);

// 处理完整系统备份
const handleSystemBackup = async () => {
  backupLoading.value.system = true;
  try {
    const response = await createSystemBackup();
    if (response.data) {
      message.success('系统备份创建成功');
      addBackupRecord('系统完整备份', '完整备份', response.data);
    }
  } catch (error: any) {
    message.error(error.message || '系统备份失败');
  } finally {
    backupLoading.value.system = false;
  }
};

// 处理数据库备份
const handleDatabaseBackup = async () => {
  backupLoading.value.database = true;
  try {
    const response = await exportDatabase();
    if (response.data) {
      message.success('数据库导出成功');
      addBackupRecord('数据库备份', '数据库', response.data);
    }
  } catch (error: any) {
    message.error(error.message || '数据库导出失败');
  } finally {
    backupLoading.value.database = false;
  }
};

// 处理文件备份
const handleFilesBackup = async () => {
  backupLoading.value.files = true;
  try {
    const response = await packUploadFiles();
    if (response.data) {
      message.success('文件打包成功');
      addBackupRecord('文件备份', '文件', response.data);
    }
  } catch (error: any) {
    message.error(error.message || '文件打包失败');
  } finally {
    backupLoading.value.files = false;
  }
};

// 添加备份记录
const addBackupRecord = (name: string, type: string, path: string) => {
  // 从路径中提取文件名
  const fileName = path.split('/').pop() || path;
  const record = {
    id: Date.now().toString(),
    name: fileName, // 使用实际的文件名
    type,
    path,
    createTime: new Date().toLocaleString()
  };
  backupHistory.value.unshift(record);

  // 保存到本地存储
  localStorage.setItem('backup_history', JSON.stringify(backupHistory.value));
};

// 下载备份文件
const downloadBackup = (path: string) => {
  // 构建完整的下载URL
  const downloadUrl = `${import.meta.env.VITE_SERVICE_BASE_URL}${path}`;
  
  // 创建临时链接进行下载
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = '';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  message.success('开始下载备份文件');
};

// 获取备份类型颜色
const getBackupTypeColor = (type: string) => {
  switch (type) {
    case '完整备份':
      return '#1890ff';
    case '数据库':
      return '#52c41a';
    case '文件':
      return '#fa8c16';
    default:
      return '#666';
  }
};

// 获取备份类型标签
const getBackupTypeTag = (type: string) => {
  switch (type) {
    case '完整备份':
      return 'info';
    case '数据库':
      return 'success';
    case '文件':
      return 'warning';
    default:
      return 'default';
  }
};

// 加载备份历史
const loadBackupHistory = () => {
  const stored = localStorage.getItem('backup_history');
  if (stored) {
    try {
      backupHistory.value = JSON.parse(stored);
    } catch (error) {
      console.error('加载备份历史失败:', error);
    }
  }
};

onMounted(() => {
  loadBackupHistory();
});
</script>

<style scoped>
.system-management {
  padding: 16px;
}
</style>
