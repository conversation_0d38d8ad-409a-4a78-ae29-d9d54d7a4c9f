<script setup lang="ts">
import { ref, reactive, onMounted, computed, h } from 'vue';
import { fetchWalletInfo, fetchTransactions, rechargeWallet, withdrawWallet } from '@/service/api';
import type { Wallet, Transaction } from '@/service/api/wallet';
import { useUserStore } from '@/store/modules/user';

defineOptions({
  name: 'Wallet'
});

const userStore = useUserStore();

// 钱包信息
const wallet = ref<Wallet | null>(null);
const loading = ref(false);

// 交易记录
const transactions = ref<Transaction[]>([]);
const transactionLoading = ref(false);
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 筛选条件
const filterForm = reactive({
  type: undefined as number | undefined,
  status: undefined as number | undefined,
  startTime: null as string | null,
  endTime: null as string | null
});

// 弹窗相关
const showRechargeModal = ref(false);
const showWithdrawModal = ref(false);
const showPaymentModal = ref(false);

// 充值演示相关
const payMethod = ref('alipay');
const paymentStep = ref(1); // 1-选择金额, 2-选择支付方式, 3-支付中, 4-支付完成
const paymentLoading = ref(false);
const paymentCountdown = ref(0);

// 表单数据
const rechargeForm = reactive({
  amount: 0
});
const withdrawForm = reactive({
  amount: 0,
  accountInfo: ''
});

// 计算属性
const availableBalance = computed(() => {
  if (!wallet.value) return 0;
  return wallet.value.balance || 0;
});

// 获取钱包信息
async function getWalletInfo() {
  loading.value = true;
  try {
    const walletRes = await fetchWalletInfo();
    wallet.value = walletRes.data;
  } catch (error) {
    console.error('获取钱包信息失败', error);
    window.$message?.error('获取钱包信息失败');
  } finally {
    loading.value = false;
  }
}

// 获取交易记录
async function getTransactions(isReset = false) {
  if (isReset) {
    pagination.page = 1;
  }

  transactionLoading.value = true;
  try {
    const res = await fetchTransactions({
      ...filterForm,
      page: pagination.page,
      size: pagination.pageSize
    });

    if (res.data?.records) {
      transactions.value = res.data.records;
      pagination.total = res.data.total;
    }
  } catch (error) {
    console.error('获取交易记录失败', error);
    window.$message?.error('获取交易记录失败');
  } finally {
    transactionLoading.value = false;
  }
}

// 开始充值流程
function startRecharge() {
  if (rechargeForm.amount <= 0) {
    window.$message?.error('充值金额必须大于0');
    return;
  }

  paymentStep.value = 2;
  showPaymentModal.value = true;
  showRechargeModal.value = false;
}

// 确认支付方式，开始支付演示
function confirmPayment() {
  paymentStep.value = 3;
  paymentLoading.value = true;
  paymentCountdown.value = 3;

  // 模拟支付过程
  const timer = setInterval(() => {
    paymentCountdown.value--;
    if (paymentCountdown.value <= 0) {
      clearInterval(timer);
      completePayment();
    }
  }, 1000);
}

// 完成支付
async function completePayment() {
  paymentLoading.value = false;
  paymentStep.value = 4;

  try {
    // 调用后端充值接口
    await rechargeWallet(rechargeForm.amount, `${payMethod.value === 'alipay' ? '支付宝' : '微信'}支付充值`);

    // 延迟1秒后关闭弹窗并刷新数据
    setTimeout(() => {
      window.$message?.success('充值成功');
      resetPaymentModal();
      getWalletInfo();
      getTransactions(true);
    }, 1500);
  } catch (error) {
    console.error('充值失败', error);
    window.$message?.error('充值失败，请重试');
    resetPaymentModal();
  }
}

// 重置支付弹窗
function resetPaymentModal() {
  showPaymentModal.value = false;
  paymentStep.value = 1;
  paymentLoading.value = false;
  paymentCountdown.value = 0;
  rechargeForm.amount = 0;
}

// 提现
async function handleWithdraw() {
  if (withdrawForm.amount <= 0) {
    window.$message?.error('提现金额必须大于0');
    return;
  }
  
  if (withdrawForm.amount > availableBalance.value) {
    window.$message?.error('可用余额不足');
    return;
  }
  
  if (!withdrawForm.accountInfo) {
    window.$message?.error('请填写收款账户信息');
    return;
  }
  
  try {
    await withdrawWallet(withdrawForm.amount, withdrawForm.accountInfo);
    window.$message?.success('提现申请已提交，等待处理');
    showWithdrawModal.value = false;
    withdrawForm.amount = 0;
    withdrawForm.accountInfo = '';
    getWalletInfo();
    getTransactions(true);
  } catch (error) {
    console.error('提现失败', error);
  }
}



// 搜索
function handleSearch() {
  getTransactions(true);
}

// 重置筛选
function resetFilter() {
  filterForm.type = undefined;
  filterForm.status = undefined;
  filterForm.startTime = null;
  filterForm.endTime = null;
  handleSearch();
}

// 获取交易类型文本
function getTransactionTypeText(type: number) {
  switch (type) {
    case 0:
      return '充值';
    case 1:
      return '消费';
    case 2:
      return '收入';
    case 3:
      return '提现';
    case 4:
      return '退款';
    case 5:
      return '转账支出';
    case 6:
      return '转账收入';
    default:
      return '未知';
  }
}

// 获取交易状态文本
function getTransactionStatusText(status: number) {
  switch (status) {
    case 0:
      return '处理中';
    case 1:
      return '成功';
    case 2:
      return '失败';
    default:
      return '未知';
  }
}

// 获取状态样式类
function getStatusClass(status: number) {
  switch (status) {
    case 0:
      return 'bg-yellow-100 text-yellow-800';
    case 1:
      return 'bg-green-100 text-green-800';
    case 2:
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

// 分页变化
function handlePageChange(page: number) {
  pagination.page = page;
  getTransactions();
}

// 获取支付弹窗标题
function getPaymentTitle() {
  switch (paymentStep.value) {
    case 2:
      return '选择支付方式';
    case 3:
      return '支付中';
    case 4:
      return '支付成功';
    default:
      return '支付';
  }
}

// 格式化日期时间
function formatDateTime(dateTime: string | Date) {
  if (!dateTime) return '-';

  const date = new Date(dateTime);
  if (isNaN(date.getTime())) return '-';

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

onMounted(() => {
  getWalletInfo();
  getTransactions();
});
</script>

<template>
  <div class="p-24px">
    <div class="flex gap-24px mb-24px">
      <!-- 钱包卡片 -->
      <NCard :bordered="false" class="flex-1 shadow-sm">
        <NSpin :show="loading">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="text-lg font-medium mb-8px">我的余额</div>
              <div class="text-4xl font-bold text-primary">{{ wallet?.balance?.toFixed(2) || '0.00' }}</div>
              <div class="mt-8px text-gray-500">
                可用余额: {{ availableBalance.toFixed(2) }} 学币
              </div>
            </div>
            <div class="flex gap-12px">
              <NButton type="primary" @click="showRechargeModal = true">充值</NButton>
              <NButton @click="showWithdrawModal = true">提现</NButton>
            </div>
          </div>
        </NSpin>
        </NCard>
    </div>
      
      <!-- 交易记录 -->
    <NCard title="交易记录" :bordered="false" class="shadow-sm">
      <!-- 筛选条件 -->
      <div class="mb-16px">
        <NForm inline :model="filterForm">
          <NFormItem label="交易类型">
            <NSelect v-model:value="filterForm.type" style="width: 140px;" :options="[
              { label: '全部类型', value: undefined },
              { label: '充值', value: 0 },
              { label: '消费', value: 1 },
              { label: '收入', value: 2 },
              { label: '提现', value: 3 },
              { label: '退款', value: 4 }
            ]" />
          </NFormItem>
          
          <NFormItem label="交易状态">
            <NSelect v-model:value="filterForm.status" style="width: 140px;" :options="[
              { label: '全部状态', value: undefined },
              { label: '处理中', value: 0 },
              { label: '成功', value: 1 },
              { label: '失败', value: 2 }
            ]" />
          </NFormItem>
          
          <NFormItem label="交易时间">
            <NDatePicker
              v-model:value="filterForm.startTime"
              type="datetime"
              placeholder="开始时间"
              style="width: 160px;"
            />
            <span class="mx-8px">至</span>
            <NDatePicker
              v-model:value="filterForm.endTime"
              type="datetime"
              placeholder="结束时间"
              style="width: 160px;"
            />
          </NFormItem>
          
          <NFormItem>
            <NButton type="primary" @click="handleSearch">搜索</NButton>
            <NButton class="ml-12px" @click="resetFilter">重置</NButton>
          </NFormItem>
        </NForm>
      </div>
      
      <!-- 交易列表 -->
      <NSpin :show="transactionLoading">
        <NDataTable
          :columns="[
            {
              title: '序号',
              key: 'index',
              width: 80,
              render: (row, index) => index + 1 + (pagination.page - 1) * pagination.pageSize
            },
            { title: '交易类型', key: 'type', width: 100, render: (row) => getTransactionTypeText(row.type) },
            {
              title: '交易金额',
              key: 'amount',
              width: 120,
              render: (row) => {
                const isIncome = row.type === 0 || row.type === 2 || row.type === 4 || row.type === 6;
                return h('span', { class: isIncome ? 'text-success' : 'text-error' },
                  `${isIncome ? '+' : '-'}${Number(row.amount).toFixed(2)}`
                );
              }
            },
            {
              title: '交易状态',
              key: 'status',
              width: 100,
              render: (row) => h('span', {
                class: `px-8px py-4px rounded text-xs ${getStatusClass(row.status)}`
              }, getTransactionStatusText(row.status))
            },
            {
              title: '交易时间',
              key: 'createdTime',
              width: 180,
              render: (row) => formatDateTime(row.createdTime)
            },
            { title: '关联订单', key: 'relatedId', width: 180, render: (row) => row.relatedId ? `${row.relatedType || ''} #${row.relatedId}` : '-' },
            { title: '备注', key: 'remark', ellipsis: { tooltip: true } }
          ]"
          :data="transactions"
          :pagination="{
            page: pagination.page,
            pageSize: pagination.pageSize,
            pageCount: Math.ceil(pagination.total / pagination.pageSize),
            showSizePicker: true,
            pageSizes: [10, 20, 30, 50],
            onChange: handlePageChange,
            onUpdatePageSize: (pageSize) => {
              pagination.pageSize = pageSize;
              pagination.page = 1;
              getTransactions();
            }
          }"
          :bordered="false"
        />
        
        <NEmpty v-if="transactions.length === 0 && !transactionLoading" description="暂无交易记录" />
      </NSpin>
        </NCard>
    
    <!-- 充值弹窗 -->
    <NModal v-model:show="showRechargeModal" preset="card" title="充值" style="width: 400px;">
      <NForm :model="rechargeForm" label-placement="left" :label-width="100">
        <NFormItem label="充值金额">
          <NInputNumber v-model:value="rechargeForm.amount" :min="0" :precision="2" style="width: 100%;" placeholder="请输入充值金额" />
        </NFormItem>

        <!-- 快捷金额选择 -->
        <NFormItem label="快捷选择">
          <div class="flex gap-8px flex-wrap">
            <NButton size="small" @click="rechargeForm.amount = 10">10元</NButton>
            <NButton size="small" @click="rechargeForm.amount = 50">50元</NButton>
            <NButton size="small" @click="rechargeForm.amount = 100">100元</NButton>
            <NButton size="small" @click="rechargeForm.amount = 200">200元</NButton>
            <NButton size="small" @click="rechargeForm.amount = 500">500元</NButton>
          </div>
        </NFormItem>

        <div class="flex justify-end gap-12px mt-16px">
          <NButton @click="showRechargeModal = false">取消</NButton>
          <NButton type="primary" :disabled="rechargeForm.amount <= 0" @click="startRecharge">
            下一步
          </NButton>
        </div>
      </NForm>
    </NModal>
    
    <!-- 提现弹窗 -->
    <NModal v-model:show="showWithdrawModal" preset="card" title="提现" style="width: 400px;">
      <NForm :model="withdrawForm" label-placement="left" :label-width="100">
        <NFormItem label="可用余额">
          <span>{{ availableBalance.toFixed(2) }}</span>
        </NFormItem>
        
        <NFormItem label="提现金额">
          <NInputNumber v-model:value="withdrawForm.amount" :min="0" :max="availableBalance" :precision="2" style="width: 100%;" />
        </NFormItem>
        
        <NFormItem label="收款账户">
          <NInput v-model:value="withdrawForm.accountInfo" placeholder="请输入收款账户信息" />
        </NFormItem>
        
        <div class="flex justify-end gap-12px mt-16px">
          <NButton @click="showWithdrawModal = false">取消</NButton>
          <NButton type="primary" 
            :disabled="withdrawForm.amount <= 0 || withdrawForm.amount > availableBalance || !withdrawForm.accountInfo" 
            @click="handleWithdraw"
          >
            确认提现
          </NButton>
        </div>
      </NForm>
    </NModal>

    <!-- 支付演示弹窗 -->
    <NModal v-model:show="showPaymentModal" preset="card" :title="getPaymentTitle()" style="width: 450px;" :closable="paymentStep !== 3">
      <!-- 选择支付方式 -->
      <div v-if="paymentStep === 2">
        <div class="mb-16px">
          <div class="text-lg font-medium mb-8px">订单信息</div>
          <div class="bg-gray-50 p-12px rounded">
            <div class="flex justify-between mb-4px">
              <span>充值金额：</span>
              <span class="font-bold text-primary">¥{{ rechargeForm.amount.toFixed(2) }}</span>
            </div>
            <div class="flex justify-between">
              <span>实付金额：</span>
              <span class="font-bold text-error">¥{{ rechargeForm.amount.toFixed(2) }}</span>
            </div>
          </div>
        </div>

        <div class="mb-16px">
          <div class="text-lg font-medium mb-8px">选择支付方式</div>
          <NRadioGroup v-model:value="payMethod" name="payMethod">
            <div class="space-y-8px">
              <div class="flex items-center p-12px border rounded cursor-pointer hover:bg-blue-50"
                   :class="{ 'border-primary bg-blue-50': payMethod === 'alipay' }"
                   @click="payMethod = 'alipay'">
                <NRadio value="alipay" class="mr-12px" />
                <div class="flex items-center">
                  <div class="w-32px h-32px bg-blue-500 rounded mr-12px flex items-center justify-center">
                    <span class="text-white text-sm font-bold">支</span>
                  </div>
                  <div>
                    <div class="font-medium">支付宝</div>
                    <div class="text-sm text-gray-500">推荐使用支付宝支付</div>
                  </div>
                </div>
              </div>

              <div class="flex items-center p-12px border rounded cursor-pointer hover:bg-green-50"
                   :class="{ 'border-success bg-green-50': payMethod === 'wechat' }"
                   @click="payMethod = 'wechat'">
                <NRadio value="wechat" class="mr-12px" />
                <div class="flex items-center">
                  <div class="w-32px h-32px bg-green-500 rounded mr-12px flex items-center justify-center">
                    <span class="text-white text-sm font-bold">微</span>
                  </div>
                  <div>
                    <div class="font-medium">微信支付</div>
                    <div class="text-sm text-gray-500">使用微信扫码支付</div>
                  </div>
                </div>
              </div>
            </div>
          </NRadioGroup>
        </div>

        <div class="flex justify-end gap-12px">
          <NButton @click="resetPaymentModal">取消</NButton>
          <NButton type="primary" @click="confirmPayment">确认支付</NButton>
        </div>
      </div>

      <!-- 支付中 -->
      <div v-if="paymentStep === 3" class="text-center py-24px">
        <NSpin size="large" />
        <div class="mt-16px text-lg">正在处理支付...</div>
        <div class="mt-8px text-gray-500">请稍候，预计还需 {{ paymentCountdown }} 秒</div>
        <div class="mt-16px">
          <div class="text-sm text-gray-500 mb-8px">支付金额</div>
          <div class="text-2xl font-bold text-primary">¥{{ rechargeForm.amount.toFixed(2) }}</div>
        </div>
      </div>

      <!-- 支付完成 -->
      <div v-if="paymentStep === 4" class="text-center py-24px">
        <div class="text-6xl text-success mb-16px">✓</div>
        <div class="text-xl font-medium mb-8px">支付成功</div>
        <div class="text-gray-500 mb-16px">您的账户已成功充值 ¥{{ rechargeForm.amount.toFixed(2) }}</div>
        <div class="bg-gray-50 p-12px rounded text-sm">
          <div class="flex justify-between mb-4px">
            <span>支付方式：</span>
            <span>{{ payMethod === 'alipay' ? '支付宝' : '微信支付' }}</span>
          </div>
          <div class="flex justify-between">
            <span>交易时间：</span>
            <span>{{ new Date().toLocaleString() }}</span>
          </div>
        </div>
      </div>
    </NModal>
  </div>
</template>

<style scoped>
.n-card :deep(.n-card-header) {
  padding-bottom: 8px;
}
</style> 