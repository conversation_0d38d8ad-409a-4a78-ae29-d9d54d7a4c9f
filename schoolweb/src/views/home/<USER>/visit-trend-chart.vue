<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { useEcharts } from '@/hooks/common/echarts';
import { fetchDashboardData } from '@/service/api';

defineOptions({
  name: 'VisitTrendChart'
});

const { domRef, updateOptions } = useEcharts(() => ({
  title: {
    text: '每日访问量趋势',
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'bold'
    }
  },
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const data = params[0];
      return `${data.name}<br/>访问量: ${data.value}`;
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [] as string[],
    axisLabel: {
      fontSize: 12
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      fontSize: 12
    }
  },
  series: [
    {
      name: '访问量',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            { offset: 0, color: '#4facfe' },
            { offset: 1, color: '#00f2fe' }
          ]
        }
      },
      itemStyle: {
        color: '#4facfe',
        borderColor: '#fff',
        borderWidth: 2
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(79, 172, 254, 0.3)' },
            { offset: 1, color: 'rgba(79, 172, 254, 0.05)' }
          ]
        }
      },
      data: [] as number[]
    }
  ]
}));

const loading = ref(false);

// 获取访问量趋势数据
async function loadVisitTrendData() {
  try {
    loading.value = true;
    const { data } = await fetchDashboardData();

    if (data && data.visitTrend) {
      const chartData = data.visitTrend;

      // 处理数据格式
      const dates = chartData.map((item: any) => {
        const date = new Date(item.date);
        return `${date.getMonth() + 1}/${date.getDate()}`;
      });
      const counts = chartData.map((item: any) => item.count);

      // 更新图表数据
      updateOptions(opts => {
        opts.xAxis.data = dates;
        opts.series[0].data = counts;
        return opts;
      });
    }
  } catch (error) {
    console.error('获取访问量趋势数据失败:', error);
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  await nextTick();
  loadVisitTrendData();
});
</script>

<template>
  <NCard :bordered="false" size="small" class="chart-wrapper">
    <NSpin :show="loading">
      <div ref="domRef" class="h-300px w-full"></div>
    </NSpin>
  </NCard>
</template>

<style scoped>
.chart-wrapper {
  min-height: 350px;
}
</style>
