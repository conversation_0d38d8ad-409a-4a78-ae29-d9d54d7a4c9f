<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { createReusableTemplate } from '@vueuse/core';
import { $t } from '@/locales';
import { fetchDashboardData } from '@/service/api';

defineOptions({
  name: 'CardData'
});

interface CardData {
  key: string;
  title: string;
  value: number;
  unit: string;
  color: {
    start: string;
    end: string;
  };
  icon: string;
}

// 数据状态
const loading = ref(false);
const statistics = ref({
  userCount: 0,
  skillCount: 0,
  orderCount: 0,
  orderAmount: 0,
  visitCount: 0
});

const cardData = computed<CardData[]>(() => [
  {
    key: 'userCount',
    title: '用户总数',
    value: statistics.value.userCount,
    unit: '',
    color: {
      start: '#ec4786',
      end: '#b955a4'
    },
    icon: 'ant-design:user-outlined'
  },
  {
    key: 'orderAmount',
    title: '成交金额',
    value: Number(statistics.value.orderAmount),
    unit: '¥',
    color: {
      start: '#865ec0',
      end: '#5144b4'
    },
    icon: 'ant-design:money-collect-outlined'
  },
  {
    key: 'visitCount',
    title: '访问总量',
    value: statistics.value.visitCount,
    unit: '',
    color: {
      start: '#56cdf3',
      end: '#719de3'
    },
    icon: 'ant-design:bar-chart-outlined'
  },
  {
    key: 'orderCount',
    title: '订单总数',
    value: statistics.value.orderCount,
    unit: '',
    color: {
      start: '#fcbc25',
      end: '#f68057'
    },
    icon: 'ant-design:trademark-circle-outlined'
  }
]);

const [DefineGradientBg, GradientBg] = createReusableTemplate<{ gradientColor: string }>();

function getGradientColor(color: CardData['color']) {
  return `linear-gradient(to bottom right, ${color.start}, ${color.end})`;
}

// 加载统计数据
async function loadStatistics() {
  try {
    loading.value = true;
    const { data } = await fetchDashboardData();
    if (data) {
      statistics.value = {
        userCount: data.userCount || 0,
        skillCount: data.skillCount || 0,
        orderCount: data.orderCount || 0,
        orderAmount: data.orderAmount || 0,
        visitCount: data.visitCount || 0
      };
    }
  } catch (error) {
    console.error('加载首页统计数据失败:', error);
    // 使用默认数据
    statistics.value = {
      userCount: 0,
      skillCount: 0,
      orderCount: 0,
      orderAmount: 0,
      visitCount: 0
    };
  } finally {
    loading.value = false;
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadStatistics();
});
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <!-- define component start: GradientBg -->
    <DefineGradientBg v-slot="{ $slots, gradientColor }">
      <div class="rd-8px px-16px pb-4px pt-8px text-white" :style="{ backgroundImage: gradientColor }">
        <component :is="$slots.default" />
      </div>
    </DefineGradientBg>
    <!-- define component end: GradientBg -->

    <NSpin :show="loading">
      <NGrid cols="s:1 m:2 l:4" responsive="screen" :x-gap="16" :y-gap="16">
        <NGi v-for="item in cardData" :key="item.key">
          <GradientBg :gradient-color="getGradientColor(item.color)" class="flex-1">
            <h3 class="text-16px">{{ item.title }}</h3>
            <div class="flex justify-between pt-12px">
              <SvgIcon :icon="item.icon" class="text-32px" />
              <CountTo
                :prefix="item.unit"
                :start-value="1"
                :end-value="item.value"
                class="text-30px text-white dark:text-dark"
              />
            </div>
          </GradientBg>
        </NGi>
      </NGrid>
    </NSpin>
  </NCard>
</template>

<style scoped>
.card-wrapper {
  min-height: 130px;
}
</style>
