<script setup lang="ts">
import { computed } from 'vue';
import { useAuthStore } from '@/store/modules/auth';
import CardData from './modules/card-data.vue';
import VisitTrendChart from './modules/visit-trend-chart.vue';

defineOptions({
  name: 'Home'
});

const authStore = useAuthStore();

// 权限检查：只有 superAdmin 可以访问首页
const isSuperAdmin = computed(() => {
  return authStore.userInfo.roles?.includes('superAdmin');
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <!-- 权限检查 -->
    <div v-if="!isSuperAdmin" class="flex-center min-h-400px">
      <NResult status="403" title="访问受限" description="只有超级管理员可以访问此页面">
        <template #footer>
          <NButton @click="$router.push('/skill')">返回技能服务</NButton>
        </template>
      </NResult>
    </div>

    <!-- superAdmin 可见的首页内容 -->
    <div v-else class="flex-col-stretch gap-16px">
      <CardData />
      <VisitTrendChart />
    </div>
  </div>
</template>

<style scoped></style>
