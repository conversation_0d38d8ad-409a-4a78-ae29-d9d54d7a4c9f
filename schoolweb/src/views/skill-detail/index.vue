<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useThemeStore } from '@/store/modules/theme';
import { useNaiveForm } from '@/hooks/common/form';
import { fetchSkillDetail, fetchSkillCategories } from '@/service/api/skill';
import { createOrder } from '@/service/api/order';
import { getFileUrl } from '@/service/api/file';
import type { SkillService } from '@/service/api/skill';
import { marked } from 'marked';
import hljs from 'highlight.js';
import katex from 'katex';
import { getCreditLevelByScore, getCreditLevelText, getCreditLevelColor } from '@/utils/creditLevel';

defineOptions({
  name: 'SkillDetail'
});

const route = useRoute();
const router = useRouter();
const themeStore = useThemeStore();
const { formRef, validate } = useNaiveForm();

const skillId = computed(() => Number(route.query.id));
const showOrderModal = ref(false);
const orderLoading = ref(false);
const loading = ref(false);
const categories = ref<any[]>([]);

// 技能详情数据
const skillDetail = ref<SkillService | null>(null);

// 配置 marked
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value;
      } catch (err) {
        console.error('Highlight error:', err);
      }
    }
    return hljs.highlightAuto(code).value;
  },
  breaks: true,
  gfm: true
});

// 渲染 Markdown 内容（包含数学公式支持）
const renderedContent = computed(() => {
  if (!skillDetail.value?.content) return '';
  try {
    let content = skillDetail.value.content;

    // 先渲染Markdown
    let html = marked(content);

    // 然后处理数学公式
    // 处理块级数学公式 $$...$$
    html = html.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
      try {
        return katex.renderToString(formula.trim(), {
          displayMode: true,
          throwOnError: false
        });
      } catch (error) {
        console.error('KaTeX block error:', error);
        return match; // 保留原始文本
      }
    });

    // 处理行内数学公式 $...$
    html = html.replace(/\$([^$\n]+?)\$/g, (match, formula) => {
      try {
        return katex.renderToString(formula.trim(), {
          displayMode: false,
          throwOnError: false
        });
      } catch (error) {
        console.error('KaTeX inline error:', error);
        return match; // 保留原始文本
      }
    });

    return html;
  } catch (error) {
    console.error('Markdown render error:', error);
    return skillDetail.value.content; // 降级到纯文本
  }
});

// 获取显示内容（优先显示详细内容，否则显示描述）
const displayContent = computed(() => {
  if (skillDetail.value?.content && skillDetail.value.content.trim()) {
    return renderedContent.value;
  }
  return '<p class="text-gray-400">暂无详细内容</p>';
});

// 是否显示详细内容区域
const hasDetailContent = computed(() => {
  return skillDetail.value?.content && skillDetail.value.content.trim();
});

// 订单信息
const orderForm = reactive({
  serviceTime: null as number | null,
  serviceHours: 2,
  contactMethod: 'online',
  contactInfo: '',
  requirements: '',
  totalPrice: computed(() => (skillDetail.value?.price || 0) * orderForm.serviceHours)
});

// 加载技能详情数据
async function loadSkillDetail() {
  if (!skillId.value) {
    window.$message?.error('技能ID不能为空');
    router.push('/skill');
    return;
  }

  try {
    loading.value = true;
    const detail = await fetchSkillDetail(skillId.value);
    skillDetail.value = detail;
  } catch (error) {
    console.error('加载技能详情失败:', error);
    window.$message?.error('加载技能详情失败');
    router.push('/skill');
  } finally {
    loading.value = false;
  }
}

// 加载分类数据
async function loadCategories() {
  try {
    const categoryList = await fetchSkillCategories();
    categories.value = Array.isArray(categoryList) ? categoryList : [];
  } catch (error) {
    console.error('加载分类失败:', error);
    categories.value = [];
  }
}

onMounted(() => {
  loadSkillDetail();
  loadCategories();
});

// 下单相关方法
function openOrderModal() {
  showOrderModal.value = true;
}

async function submitOrder() {
  try {
    await validate();
    orderLoading.value = true;

    if (!skillDetail.value) {
      window.$message?.error('技能服务信息不存在');
      return;
    }

    if (!orderForm.serviceTime) {
      window.$message?.error('请选择服务时间');
      return;
    }

    // 构建订单数据
    const orderData = {
      skillId: skillDetail.value.serviceId,
      quantity: orderForm.serviceHours,
      appointmentTime: orderForm.serviceTime,
      appointmentAddress: orderForm.contactMethod === 'offline' ? orderForm.contactInfo : '线上服务',
      appointmentDuration: orderForm.serviceHours,
      appointmentRemark: `联系方式：${orderForm.contactMethod === 'online' ? '线上' : '线下'}\n联系信息：${orderForm.contactInfo}\n具体需求：${orderForm.requirements || '无特殊需求'}`
    };

    const response = await createOrder(orderData);

    if (response && response.data && response.data.orderId) {
      orderLoading.value = false;
      showOrderModal.value = false;

      window.$message?.success('预约服务成功！订单已创建，请前往支付');

      // 跳转到订单详情页面或订单列表
      router.push(`/order?orderId=${response.data.orderId}`);
    } else {
      orderLoading.value = false;
      window.$message?.error('订单创建失败，请重试');
    }

  } catch (error: any) {
    orderLoading.value = false;
    console.error('创建订单失败:', error);
    window.$message?.error(error.message || '预约失败，请重试');
  }
}

// 联系服务提供者
function contactProvider() {
  window.$message?.info('已向服务提供者发送联系请求');
}

// 收藏服务
function toggleFavorite() {
  window.$message?.success('收藏成功');
}

// 格式化技能分类名称
function getCategoryName(categoryId: number) {
  if (!Array.isArray(categories.value)) {
    return '其他技能';
  }
  const category = categories.value.find(item => item.categoryId === categoryId);
  return category ? category.name : '其他技能';
}

// 获取技能封面图片
function getSkillCoverImage() {
  if (skillDetail.value?.coverImage) {
    return getFileUrl(skillDetail.value.coverImage);
  }
  if (skillDetail.value?.images && skillDetail.value.images.length > 0) {
    return getFileUrl(skillDetail.value.images[0]);
  }
  return `https://picsum.photos/800/400?random=${skillDetail.value?.serviceId || 1}`;
}
</script>

<template>
  <div class="p-24px">
    <div class="flex items-center mb-16px">
      <NButton quaternary circle @click="router.back()">
        <div class="i-material-symbols:arrow-back text-18px"></div>
      </NButton>
      <span class="ml-8px">返回技能列表</span>
    </div>

    <NSpin :show="loading">
      <div v-if="skillDetail">
        <NGrid :cols="24" :x-gap="24" :y-gap="24" responsive="screen" item-responsive>
      <!-- 技能详情部分 -->
      <NGi span="24 s:24 m:16">
        <NCard :bordered="false" size="large" class="rounded-16px shadow-sm">
          <div class="relative">
            <div class="h-240px bg-gray-200 rounded-8px overflow-hidden">
              <img
                :src="getSkillCoverImage()"
                class="w-full h-full object-cover"
                alt="技能封面"
              />
            </div>

            <div class="mt-16px">
              <div class="flex items-start justify-between">
                <div>
                  <div class="flex-y-center">
                    <NTag :bordered="false" round size="small" type="info">
                      {{ getCategoryName(skillDetail.categoryId || 0) }}
                    </NTag>
                    <span class="ml-8px flex-y-center">
                      <div class="i-material-symbols:star text-yellow-500"></div>
                      <span class="ml-4px">{{ skillDetail.ratingAvg || 0 }}</span>
                      <span class="ml-4px text-12px text-gray-400">({{ skillDetail.ratingCount || 0 }})</span>
                    </span>
                  </div>
                  <h1 class="text-24px font-bold mt-8px">{{ skillDetail.title || '未知技能' }}</h1>
                </div>
                <div class="flex">
                  <NButton circle @click="toggleFavorite">
                    <template #icon>
                      <div class="i-material-symbols:favorite-outline"></div>
                    </template>
                  </NButton>
                  <NButton class="ml-8px" circle @click="contactProvider">
                    <template #icon>
                      <div class="i-material-symbols:chat-outline"></div>
                    </template>
                  </NButton>
                </div>
              </div>
              
              <div class="flex-wrap mt-8px" v-if="skillDetail.tags && skillDetail.tags.length > 0">
                <NSpace>
                  <NTag
                    v-for="tag in skillDetail.tags"
                    :key="tag"
                    size="small"
                    :bordered="false"
                    :color="{ color: '#e9f5fe', textColor: '#2080f0' }"
                  >
                    {{ tag }}
                  </NTag>
                </NSpace>
              </div>

              <div class="mt-16px pb-16px border-b border-gray-200">
                <div class="text-gray-700">{{ skillDetail.description || '暂无描述' }}</div>
                <div class="mt-16px text-24px font-bold text-primary">
                  {{ skillDetail.price || 0 }} <span class="text-14px text-gray-500 font-normal">学币</span>
                </div>
                <div class="mt-8px text-14px text-gray-500">
                  服务时长：{{ skillDetail.duration || 0 }}分钟 |
                  服务方式：{{ skillDetail.serviceType === 1 ? '线上' : '线下' }}
                  <span v-if="skillDetail.location"> | 地点：{{ skillDetail.location }}</span>
                </div>
              </div>

              <!-- 详细内容部分 -->
              <div v-if="hasDetailContent" class="mt-16px">
                <h3 class="text-16px font-bold mb-12px">服务详情</h3>
                <div v-html="displayContent" class="skill-content markdown-body"></div>
              </div>
              

            </div>
          </div>
        </NCard>

      </NGi>
      
      <!-- 侧边栏 -->
      <NGi span="24 s:24 m:8">
        <!-- 服务提供者信息 -->
        <NCard :bordered="false" size="large" class="rounded-16px shadow-sm">
          <div>
            <h2 class="text-18px font-bold mb-16px">服务提供者</h2>
            <div class="flex items-center">
              <NAvatar
                :src="skillDetail.userAvatar || ''"
                round
                size="large"
                :style="{ backgroundColor: themeStore.themeColor }"
              >
                {{ skillDetail.userName?.charAt(0) || '?' }}
              </NAvatar>
              <div class="ml-16px">
                <div class="font-medium">{{ skillDetail.userName || '未知用户' }}</div>
                <div class="text-14px text-gray-500">{{ skillDetail.userAcademy || '未知学院' }}</div>
              </div>
            </div>

            <div class="mt-16px grid grid-cols-2 gap-16px">
              <div class="text-center">
                <div class="text-20px font-bold text-primary">{{ skillDetail.orderCount || 0 }}</div>
                <div class="text-14px text-gray-500">已完成订单</div>
              </div>
              <div class="text-center">
                <div class="text-20px font-bold text-primary">{{ skillDetail.viewCount || 0 }}</div>
                <div class="text-14px text-gray-500">浏览次数</div>
              </div>
            </div>

            <div class="mt-16px">
              <div class="flex-y-center">
                <div class="i-material-symbols:verified text-primary"></div>
                <span class="ml-8px text-14px">信用评级：</span>
                <span class="text-16px font-medium" :style="{ color: (skillDetail.userCreditScore || 0) >= 90 ? '#18a058' : '#2080f0' }">
                  {{ (skillDetail.userCreditScore || 0) >= 90 ? 'A级' : 'B级' }}
                </span>
              </div>
            </div>
            
            <div class="mt-24px">
              <NButton type="primary" block @click="openOrderModal">预约服务</NButton>
              <NButton class="mt-16px" secondary block @click="contactProvider">联系Ta</NButton>
            </div>
          </div>
        </NCard>
      </NGi>
    </NGrid>
      </div>

      <!-- 空状态 -->
      <NResult
        v-else-if="!loading"
        status="404"
        title="技能服务不存在"
        description="该技能服务可能已被删除或不存在"
      >
        <template #footer>
          <NButton type="primary" @click="router.push('/skill')">返回技能列表</NButton>
        </template>
      </NResult>
    </NSpin>
    
    <!-- 下单弹窗 -->
    <NModal
      v-model:show="showOrderModal"
      preset="card"
      title="预约服务"
      style="width: 600px"
      :bordered="false"
      :segmented="true"
      size="huge"
      :mask-closable="false"
    >
      <NForm
        ref="formRef"
        :model="orderForm"
        label-placement="left"
        label-width="100"
        require-mark-placement="right-hanging"
      >
        <NFormItem label="服务时间" path="serviceTime" :rule="{ required: true, message: '请选择服务时间' }">
          <NDatePicker
            v-model:value="orderForm.serviceTime"
            type="datetime"
            clearable
            style="width: 100%"
            :is-date-disabled="(ts: number) => ts < Date.now()"
          />
        </NFormItem>
        
        <NFormItem label="服务时长" path="serviceHours" :rule="{ required: true, message: '请选择服务时长' }">
          <NInputNumber
            v-model:value="orderForm.serviceHours"
            :min="1"
            :max="10"
            style="width: 100%"
          >
            <template #suffix>小时</template>
          </NInputNumber>
        </NFormItem>
        
        <NFormItem label="联系方式" path="contactMethod" :rule="{ required: true, message: '请选择联系方式' }">
          <NRadioGroup v-model:value="orderForm.contactMethod">
            <NRadio value="online">线上（腾讯会议/微信视频）</NRadio>
            <NRadio value="offline">线下面对面</NRadio>
          </NRadioGroup>
        </NFormItem>
        
        <NFormItem label="联系信息" path="contactInfo" :rule="{ required: true, message: '请填写联系信息' }">
          <NInput
            v-model:value="orderForm.contactInfo"
            placeholder="线上请填写微信号或QQ号，线下请填写校区与约定地点"
          />
        </NFormItem>
        
        <NFormItem label="具体需求" path="requirements">
          <NInput
            v-model:value="orderForm.requirements"
            type="textarea"
            placeholder="请详细描述你的具体需求或问题，以便服务者更好地准备"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </NFormItem>
        
        <div class="py-16px flex-y-center justify-between border-t border-gray-200 mt-16px">
          <div>
            <span class="text-14px">服务费用：</span>
            <span class="text-20px font-bold text-primary">{{ orderForm.totalPrice }}</span>
            <span class="text-14px text-gray-500"> 学币</span>
          </div>
          <div>
            <NButton class="mr-16px" @click="showOrderModal = false">取消</NButton>
            <NButton type="primary" :loading="orderLoading" @click="submitOrder">确认预约</NButton>
          </div>
        </div>
      </NForm>
    </NModal>
  </div>
</template>

<style scoped>
/* 导入 KaTeX 样式 */
@import 'katex/dist/katex.min.css';

/* Markdown 样式 */
.skill-content.markdown-body {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.skill-content.markdown-body :deep(h1),
.skill-content.markdown-body :deep(h2),
.skill-content.markdown-body :deep(h3),
.skill-content.markdown-body :deep(h4),
.skill-content.markdown-body :deep(h5),
.skill-content.markdown-body :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.25;
}

.skill-content.markdown-body :deep(h1) { font-size: 24px; }
.skill-content.markdown-body :deep(h2) { font-size: 20px; }
.skill-content.markdown-body :deep(h3) { font-size: 18px; }

.skill-content.markdown-body :deep(p) {
  margin: 8px 0;
}

.skill-content.markdown-body :deep(ul),
.skill-content.markdown-body :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.skill-content.markdown-body :deep(li) {
  margin: 4px 0;
}

.skill-content.markdown-body :deep(code) {
  background-color: #f6f8fa;
  border-radius: 3px;
  font-size: 85%;
  margin: 0;
  padding: 2px 4px;
}

.skill-content.markdown-body :deep(pre) {
  background-color: #f6f8fa;
  border-radius: 6px;
  font-size: 85%;
  line-height: 1.45;
  overflow: auto;
  padding: 16px;
  margin: 8px 0;
}

.skill-content.markdown-body :deep(pre code) {
  background-color: transparent;
  border: 0;
  display: inline;
  line-height: inherit;
  margin: 0;
  max-width: auto;
  overflow: visible;
  padding: 0;
  word-wrap: normal;
}

.skill-content.markdown-body :deep(blockquote) {
  border-left: 4px solid #dfe2e5;
  margin: 8px 0;
  padding: 0 16px;
  color: #6a737d;
}

.skill-content.markdown-body :deep(a) {
  color: #0366d6;
  text-decoration: none;
}

.skill-content.markdown-body :deep(a:hover) {
  text-decoration: underline;
}

.skill-content.markdown-body :deep(strong) {
  font-weight: 600;
}

.skill-content.markdown-body :deep(em) {
  font-style: italic;
}

.skill-content.markdown-body :deep(table) {
  border-collapse: collapse;
  margin: 8px 0;
  width: 100%;
}

.skill-content.markdown-body :deep(table th),
.skill-content.markdown-body :deep(table td) {
  border: 1px solid #dfe2e5;
  padding: 6px 13px;
}

.skill-content.markdown-body :deep(table th) {
  background-color: #f6f8fa;
  font-weight: 600;
}

/* 数学公式样式 */
.skill-content.markdown-body :deep(.katex) {
  font-size: 1.1em;
}

.skill-content.markdown-body :deep(.katex-display) {
  margin: 16px 0;
  text-align: center;
}

.skill-content.markdown-body :deep(.katex-display .katex) {
  display: inline-block;
  white-space: nowrap;
}
</style>