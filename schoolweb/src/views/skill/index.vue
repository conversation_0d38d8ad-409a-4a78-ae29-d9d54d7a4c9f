<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useThemeStore } from '@/store/modules/theme';
import { fetchSkillList, fetchSkillCategories, type SkillService, type SkillCategory } from '@/service/api/skill';
import { getFileUrl } from '@/service/api/file';

defineOptions({
  name: 'Skill'
});

const router = useRouter();
const themeStore = useThemeStore();

// 搜索相关
const searchValue = ref('');
const showSearchPanel = ref(false);
const selectedCategories = ref<number[]>([]);
const priceRange = ref([0, 1000]);

// 数据状态
const loading = ref(false);
const skillList = ref<SkillService[]>([]);
const categoryOptions = ref<{ label: string; value: number }[]>([]);
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  pages: 0
});

// 加载分类数据
async function loadCategories() {
  try {
    const response = await fetchSkillCategories();

    if (Array.isArray(response)) {
      categoryOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.categoryId
      }));
    } else if (response && response.data && Array.isArray(response.data)) {
      categoryOptions.value = response.data.map((item: any) => ({
        label: item.name,
        value: item.categoryId
      }));
    } else if (response && typeof response === 'object') {
      for (const [key, value] of Object.entries(response)) {
        if (Array.isArray(value)) {
          categoryOptions.value = value.map((item: any) => ({
            label: item.name,
            value: item.categoryId
          }));
          break;
        }
      }
    }
  } catch (error) {
    console.error('加载分类失败:', error);
    window.$message?.error('加载分类失败');
  }
}

// 加载技能列表
async function loadSkillList(isRefresh = false) {
  if (isRefresh) {
    pagination.page = 1;
  }

  loading.value = true;
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchValue.value || undefined,
      categoryId: selectedCategories.value.length > 0 ? selectedCategories.value[0] : undefined,
      priceMin: priceRange.value[0],
      priceMax: priceRange.value[1],
      sortBy: 'created_time'
    };

    const response = await fetchSkillList(params);

    if (response) {
      skillList.value = response.records || [];
      pagination.total = response.total || 0;
      pagination.pages = response.pages || 0;
    }
  } catch (error) {
    console.error('加载技能列表失败:', error);
    window.$message?.error('加载技能列表失败');
  } finally {
    loading.value = false;
  }
}

// 初始化数据
onMounted(async () => {
  await loadCategories();
  await loadSkillList();
});

// 监听搜索条件变化
watch([searchValue, selectedCategories, priceRange], () => {
  loadSkillList(true);
}, { deep: true });

// 跳转到技能详情页
function goToSkillDetail(serviceId: number) {
  router.push(`/skill-detail?id=${serviceId}`);
}

// 跳转到发布技能页面
function goToPublishSkill() {
  router.push('/skill-publish');
}

// 清空筛选条件
function clearFilters() {
  searchValue.value = '';
  selectedCategories.value = [];
  priceRange.value = [0, 1000];
  showSearchPanel.value = false;
}

// 获取分类名称
function getCategoryName(categoryId: number) {
  const category = categoryOptions.value.find(item => item.value === categoryId);
  return category ? category.label : '';
}

// 获取技能封面图片
function getSkillCoverImage(skill: SkillService) {
  if (skill.coverImage) {
    return getFileUrl(skill.coverImage);
  }
  return `https://picsum.photos/400/250?random=${skill.serviceId}`;
}

// 加载更多
function loadMore() {
  if (pagination.page < pagination.pages) {
    pagination.page += 1;
    loadSkillList();
  }
}

// 刷新列表
function refreshList() {
  loadSkillList(true);
}
</script>

<template>
  <div class="p-24px">
    <NCard :bordered="false" size="large" class="rounded-16px shadow-sm mb-16px">
      <div class="flex justify-between items-center">
        <div class="flex-1 max-w-800px">
          <NInput
            v-model:value="searchValue"
            placeholder="搜索技能、服务或技能标签"
            clearable
            size="large"
          >
            <template #prefix>
              <NIcon class="text-gray-400">
                <div class="i-material-symbols:search"></div>
              </NIcon>
            </template>
            <template #suffix>
              <NButton text @click="showSearchPanel = !showSearchPanel">
                <NIcon size="20">
                  <div class="i-material-symbols:filter-alt"></div>
                </NIcon>
              </NButton>
            </template>
          </NInput>
        </div>
        <div class="ml-16px">
          <NButton type="primary" size="large" @click="goToPublishSkill">
            <template #icon>
              <NIcon>
                <div class="i-material-symbols:add"></div>
              </NIcon>
            </template>
            发布我的技能
          </NButton>
        </div>
      </div>
      
      <!-- 高级搜索面板 -->
      <NCollapse v-if="showSearchPanel">
        <NCollapseItem title="高级筛选" name="1">
          <div class="py-16px">
            <div class="mb-16px">
              <div class="mb-8px font-medium">服务分类</div>
              <NSpace>
                <NCheckboxGroup v-model:value="selectedCategories">
                  <NSpace>
                    <NCheckbox
                      v-for="item in categoryOptions"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label"
                    />
                  </NSpace>
                </NCheckboxGroup>
              </NSpace>
            </div>
            
            <div class="mb-16px">
              <div class="mb-8px font-medium">价格区间</div>
              <NSlider
                v-model:value="priceRange"
                range
                :min="0"
                :max="1000"
                :step="10"
                style="max-width: 500px"
              />
              <div class="mt-4px text-14px text-gray-500">
                {{ priceRange[0] }} 学币 - {{ priceRange[1] }} 学币
              </div>
            </div>
            
            <div class="flex justify-end">
              <NButton @click="clearFilters" class="mr-16px">清空筛选</NButton>
              <NButton type="primary" @click="showSearchPanel = false">确定</NButton>
            </div>
          </div>
        </NCollapseItem>
      </NCollapse>
    </NCard>
    
    <!-- 技能列表 -->
    <NSpin :show="loading">
      <div v-if="skillList.length > 0">
        <NGrid :cols="24" :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
          <NGi
            v-for="skill in skillList"
            :key="skill.serviceId"
            span="24 s:12 m:8 l:6"
          >
            <NCard
              :bordered="false"
              size="small"
              class="rounded-16px shadow-sm cursor-pointer hover:shadow-md transition-all duration-300"
              hoverable
              @click="goToSkillDetail(skill.serviceId)"
            >
              <template #cover>
                <div class="h-160px bg-gray-200 rounded-t-16px flex-center">
                  <img
                    :src="getSkillCoverImage(skill)"
                    class="w-full h-full object-cover rounded-t-16px"
                    alt="技能封面"
                  />
                </div>
              </template>

              <div class="pt-8px">
                <div class="flex-y-center">
                  <NTag :bordered="false" round size="small" type="info">
                    {{ getCategoryName(skill.categoryId) }}
                  </NTag>
                  <div class="flex-1"></div>
                  <div class="flex-y-center">
                    <div class="i-material-symbols:star text-yellow-500"></div>
                    <span class="ml-4px text-14px">{{ skill.ratingAvg || 0 }}</span>
                    <span class="ml-4px text-12px text-gray-400">({{ skill.ratingCount || 0 }})</span>
                  </div>
                </div>

                <div class="font-medium text-16px line-clamp-1 mt-8px">{{ skill.title }}</div>

                <div class="text-gray-500 text-14px line-clamp-2 mt-4px h-40px">{{ skill.description }}</div>

                <div class="flex-y-center mt-8px">
                  <NAvatar
                    :src="skill.userAvatar || ''"
                    round
                    size="small"
                    :style="{ backgroundColor: themeStore.themeColor }"
                  >
                    {{ skill.userName?.charAt(0) || '?' }}
                  </NAvatar>
                  <span class="ml-8px text-14px">{{ skill.userName || '未知用户' }}</span>
                  <div class="flex-1"></div>
                  <div class="text-16px font-medium text-primary">{{ skill.price }} <span class="text-12px text-gray-500">学币</span></div>
                </div>
              </div>
            </NCard>
          </NGi>
        </NGrid>

        <!-- 分页 -->
        <div class="flex justify-center mt-24px" v-if="pagination.pages > 1">
          <NPagination
            v-model:page="pagination.page"
            :page-count="pagination.pages"
            :page-size="pagination.size"
            :item-count="pagination.total"
            show-size-picker
            :page-sizes="[10, 20, 50]"
            @update:page="loadSkillList"
            @update:page-size="(size: number) => { pagination.size = size; loadSkillList(true); }"
          />
        </div>
      </div>
    </NSpin>
    
    <!-- 空状态 -->
    <NResult
      v-if="!loading && skillList.length === 0"
      status="404"
      title="暂无数据"
      description="没有找到符合条件的技能服务，请尝试其他搜索条件"
    >
      <template #footer>
        <NSpace>
          <NButton @click="clearFilters">清空筛选条件</NButton>
          <NButton type="primary" @click="refreshList">刷新列表</NButton>
        </NSpace>
      </template>
    </NResult>
  </div>
</template>

<style scoped>
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
</style> 