<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { fetchPostDetail, fetchComments, createComment, likePost, likeComment, followUser } from '@/service/api/social';
import type { Post, Comment } from '@/service/api/social';
import { getFileUrl } from '@/service/api/file';
import { marked } from 'marked';
import hljs from 'highlight.js';
import katex from 'katex';
import { getUserTypeText, getUserTypeColor } from '@/utils/userType';

defineOptions({
  name: 'SocialPostDetail'
});

// 定义 props
interface Props {
  id?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  id: ''
});

const route = useRoute();
const router = useRouter();

// 优先使用 props，然后使用路由参数
const postId = ref(Number(props.id || route.params.id));

console.log('=== 动态详情页面组件初始化 ===');
console.log('Props:', props);
console.log('Route params:', route.params);
console.log('Post ID:', postId.value);
console.log('组件加载完成');

// 初始化数据的函数
function initData() {
  console.log('🔄 初始化数据');
  console.log('当前路由参数:', route.params);
  console.log('当前props:', props);

  const newId = props.id || route.params.id;
  console.log('获取到的ID:', newId);

  if (newId) {
    postId.value = Number(newId);
    console.log('设置postId为:', postId.value);

    if (!isNaN(postId.value)) {
      // 重置数据
      post.value = null;
      comments.value = [];

      // 加载新数据
      getPostDetail();
      getComments();
    } else {
      console.error('❌ 无效的动态ID:', newId);
    }
  } else {
    console.error('❌ 未找到动态ID');
  }
}

// 监听路由变化
watch(() => route.params.id, (newId, oldId) => {
  console.log('📍 路由参数变化:', { newId, oldId });
  if (newId && newId !== oldId) {
    initData();
  }
}, { immediate: false });

// 监听 props 变化
watch(() => props.id, (newId, oldId) => {
  console.log('📦 Props ID 变化:', { newId, oldId });
  if (newId && newId !== oldId) {
    initData();
  }
}, { immediate: false });

// 动态详情
const post = ref<Post | null>(null);
const loading = ref(false);

// 配置 marked
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value;
      } catch (err) {
        console.error('Highlight error:', err);
      }
    }
    return hljs.highlightAuto(code).value;
  },
  breaks: true,
  gfm: true
});

// 渲染 Markdown 内容（包含数学公式支持）
const renderedContent = computed(() => {
  if (!post.value?.content) return '';
  try {
    let content = post.value.content;

    // 先渲染Markdown
    let html = marked(content);

    // 然后处理数学公式
    // 处理块级数学公式 $$...$$
    html = html.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
      try {
        return katex.renderToString(formula.trim(), {
          displayMode: true,
          throwOnError: false
        });
      } catch (error) {
        console.error('KaTeX block error:', error);
        return match; // 保留原始文本
      }
    });

    // 处理行内数学公式 $...$
    html = html.replace(/\$([^$\n]+?)\$/g, (match, formula) => {
      try {
        return katex.renderToString(formula.trim(), {
          displayMode: false,
          throwOnError: false
        });
      } catch (error) {
        console.error('KaTeX inline error:', error);
        return match; // 保留原始文本
      }
    });

    return html;
  } catch (error) {
    console.error('Markdown render error:', error);
    return post.value.content; // 降级到纯文本
  }
});

// 评论列表
const comments = ref<Comment[]>([]);
const commentLoading = ref(false);
const commentPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
});

// 发表评论
const commentContent = ref('');
const replyTo = ref<{
  commentId: number;
  userId: number;
  userName: string;
} | null>(null);

// 格式化时间
function formatTime(timeStr: string | number) {
  try {
    const date = new Date(timeStr);
    if (isNaN(date.getTime())) {
      return '时间未知';
    }
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (error) {
    console.error('时间格式化错误:', error);
    return '时间未知';
  }
}

// 获取动态详情
async function getPostDetail() {
  console.log('=== 开始获取动态详情 ===');
  console.log('当前postId:', postId.value);
  console.log('postId类型:', typeof postId.value);
  console.log('postId是否为NaN:', isNaN(postId.value));

  if (!postId.value || isNaN(postId.value)) {
    console.error('无效的动态ID:', postId.value);
    window.$message?.error('无效的动态ID');
    return;
  }

  loading.value = true;
  try {
    console.log('调用API: fetchPostDetail, 参数:', postId.value);
    const res = await fetchPostDetail(postId.value);
    console.log('API响应原始数据:', res);
    console.log('响应数据类型:', typeof res);
    console.log('响应是否为null/undefined:', res == null);

    // 处理不同的响应格式
    if (res && res.data) {
      console.log('使用 res.data 格式');
      post.value = res.data;
    } else if (res) {
      console.log('使用直接 res 格式');
      post.value = res;
    } else {
      console.warn('动态详情响应为空');
      window.$message?.error('动态不存在');
    }

    console.log('最终设置的动态数据:', post.value);
    console.log('=== 获取动态详情完成 ===');
  } catch (error) {
    console.error('获取动态详情失败', error);
    console.error('错误详情:', error.message, error.stack);
    window.$message?.error('获取动态详情失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
}

// 获取评论列表
async function getComments(isRefresh = false) {
  if (isRefresh) {
    commentPagination.page = 1;
  }
  commentLoading.value = true;
  try {
    const res = await fetchComments(postId.value, {
      page: commentPagination.page,
      pageSize: commentPagination.pageSize
    });

    console.log('获取评论响应:', res);

    // 处理不同的响应格式
    let records = null;
    let total = 0;

    if (res && res.data && res.data.records) {
      records = res.data.records;
      total = res.data.total || 0;
    } else if (res && Array.isArray(res.data)) {
      records = res.data;
      total = res.data.length;
    } else if (Array.isArray(res)) {
      records = res;
      total = res.length;
    }

    if (records && Array.isArray(records)) {
      if (isRefresh) {
        comments.value = records;
      } else {
        comments.value = [...comments.value, ...records];
      }
      commentPagination.total = total;
    }
  } catch (error) {
    console.error('获取评论失败', error);
  } finally {
    commentLoading.value = false;
  }
}

// 加载更多评论
function loadMoreComments() {
  if (comments.value.length < commentPagination.total) {
    commentPagination.page += 1;
    getComments();
  }
}

// 点赞/取消点赞动态
async function handleLikePost() {
  if (!post.value) return;
  try {
    await likePost(post.value.id, !post.value.isLiked);
    post.value.isLiked = !post.value.isLiked;
    post.value.likeCount = post.value.isLiked ? post.value.likeCount + 1 : post.value.likeCount - 1;
  } catch (error) {
    console.error('操作失败', error);
  }
}

// 点赞/取消点赞评论
async function handleLikeComment(comment: Comment) {
  try {
    await likeComment(comment.id, !comment.isLiked);
    comment.isLiked = !comment.isLiked;
    comment.likeCount = comment.isLiked ? comment.likeCount + 1 : comment.likeCount - 1;
  } catch (error) {
    console.error('操作失败', error);
  }
}

// 关注/取消关注用户
async function handleFollow() {
  if (!post.value) return;
  try {
    await followUser(post.value.userId, !post.value.isFollowed);
    post.value.isFollowed = !post.value.isFollowed;
  } catch (error) {
    console.error('操作失败', error);
  }
}

// 回复评论
function replyToComment(comment: Comment) {
  replyTo.value = {
    commentId: comment.id,
    userId: comment.userId,
    userName: comment.userName || ''
  };
  // 聚焦评论输入框
  document.getElementById('comment-input')?.focus();
}

// 取消回复
function cancelReply() {
  replyTo.value = null;
}

// 发表评论
async function submitComment() {
  if (!commentContent.value.trim()) {
    window.$message?.error('评论内容不能为空');
    return;
  }
  
  try {
    await createComment(postId.value, {
      content: commentContent.value,
      parentId: replyTo.value?.commentId,
      replyToUserId: replyTo.value?.userId
    });
    window.$message?.success('评论成功');
    commentContent.value = '';
    replyTo.value = null;
    getComments(true);
    // 刷新动态信息以更新评论数
    getPostDetail();
  } catch (error) {
    console.error('评论失败', error);
  }
}

// 返回上一页
function goBack() {
  router.back();
}

onMounted(() => {
  console.log('🚀 onMounted 钩子执行');
  console.log('开始初始化数据');
  initData();
});
</script>

<template>
  <div class="p-24px">
    <div class="mb-16px">
      <NButton @click="goBack">
        <template #icon>
          <NIcon>
            <div class="i-material-symbols:arrow-back"></div>
          </NIcon>
        </template>
        返回
      </NButton>
    </div>

    <!-- 组件加载测试 -->
    <div class="mb-16px p-16px bg-blue-50 border border-blue-200 rounded">
      <h3 class="text-blue-800 font-bold">🔍 组件状态测试</h3>
      <div>✅ 组件已加载</div>
      <div>📍 路由路径: {{ route.path }}</div>
      <div>🆔 动态ID: {{ postId }}</div>
      <div>⏳ 加载状态: {{ loading }}</div>
      <div>📄 是否有动态数据: {{ !!post }}</div>
      <div>🔢 动态数据类型: {{ typeof post }}</div>
    </div>

    <!-- 调试信息 -->
    <div v-if="!loading && !post" class="mb-16px p-16px bg-gray-50 rounded">
      <div>详细调试信息:</div>
      <div>Props: {{ props }}</div>
      <div>路由参数: {{ route.params }}</div>
      <div>动态ID: {{ postId }}</div>
      <div>加载状态: {{ loading }}</div>
      <div>动态数据: {{ post }}</div>
    </div>

    <NSpin :show="loading">
      <div v-if="post" class="flex gap-24px">
        <!-- 左侧 - 动态内容 -->
        <div class="flex-1">
          <NCard :bordered="false" class="shadow-sm mb-16px">
            <!-- 用户信息 -->
            <div class="flex justify-between items-center mb-16px">
              <div class="flex items-center gap-12px cursor-pointer" @click="router.push(`/user/${post.userId}`)">
                <NAvatar v-if="post.userAvatar" :src="post.userAvatar" size="large" />
                <NAvatar v-else size="large">{{ post.userName?.substring(0, 1) || '用' }}</NAvatar>
                <div class="flex-1">
                  <div class="flex items-center gap-8px">
                    <span class="font-medium text-lg">{{ post.userName }}</span>
                    <NTag v-if="getUserTypeText(post.userType)" :type="getUserTypeColor(post.userType)" size="small">
                      {{ getUserTypeText(post.userType) }}
                    </NTag>
                  </div>
                  <span class="text-sm text-gray-500">{{ formatTime(post.createdTime) }}</span>
                </div>
              </div>
              <NButton 
                v-if="post.userId !== $auth?.userId" 
                :type="post.isFollowed ? 'default' : 'primary'" 
                @click="handleFollow"
              >
                {{ post.isFollowed ? '已关注' : '关注' }}
              </NButton>
            </div>
            
            <!-- 动态内容 -->
            <div class="mb-24px">
              <div v-html="renderedContent" class="post-content markdown-body mb-16px"></div>
              
              <!-- 图片 -->
              <div v-if="post.images && post.images.length > 0" class="grid gap-12px mb-16px">
                <div v-for="(img, imgIndex) in post.images" :key="imgIndex">
                  <img :src="getFileUrl(img)" alt="动态图片" class="max-w-full rounded-4px" />
                </div>
              </div>
              
              <!-- 关联技能 -->
              <NTag v-if="post.skillId" type="info" class="mt-8px" @click="router.push(`/skill/${post.skillId}`)">
                {{ post.skillTitle }}
              </NTag>
            </div>
            
            <!-- 交互区 -->
            <div class="flex border-t border-gray-100 pt-16px text-gray-500">
              <div class="flex-1 flex items-center gap-4px cursor-pointer hover:text-primary" 
                @click="handleLikePost"
              >
                <NIcon>
                  <div v-if="post.isLiked" class="i-material-symbols:favorite text-red-500"></div>
                  <div v-else class="i-material-symbols:favorite-outline"></div>
                </NIcon>
                <span>{{ post.likeCount || 0 }}</span>
              </div>
            </div>
          </NCard>
          
          <!-- 评论区 -->
          <NCard title="评论" :bordered="false" class="shadow-sm">
            <!-- 评论输入框 -->
            <div class="mb-24px">
              <div v-if="replyTo" class="flex items-center mb-8px text-sm text-gray-500">
                <span>回复 @{{ replyTo.userName }}：</span>
                <NButton text size="tiny" @click="cancelReply">取消</NButton>
              </div>
              <NInput
                id="comment-input"
                v-model:value="commentContent"
                type="textarea"
                :rows="3"
                placeholder="写下你的评论..."
              />
              <div class="flex justify-end mt-8px">
                <NButton type="primary" :disabled="!commentContent.trim()" @click="submitComment">
                  发表评论
                </NButton>
              </div>
            </div>
            
            <!-- 评论列表 -->
            <NSpin :show="commentLoading">
              <NEmpty v-if="comments.length === 0" description="暂无评论" />
              <div v-else>
                <div v-for="comment in comments" :key="comment.id" class="py-16px border-b border-gray-100 last:border-0">
                  <!-- 评论用户信息 -->
                  <div class="flex gap-12px">
                    <NAvatar v-if="comment.userAvatar" :src="comment.userAvatar" size="small" />
                    <NAvatar v-else size="small">{{ comment.userName?.substring(0, 1) || '用' }}</NAvatar>
                    <div class="flex-1">
                      <div class="flex items-baseline gap-8px">
                        <span class="font-medium">{{ comment.userName }}</span>
                        <NTag v-if="getUserTypeText(comment.userType)" :type="getUserTypeColor(comment.userType)" size="tiny">
                          {{ getUserTypeText(comment.userType) }}
                        </NTag>
                        <span class="text-xs text-gray-400">{{ formatTime(comment.createdTime) }}</span>
                      </div>
                      
                      <!-- 回复信息 -->
                      <div v-if="comment.replyToUserName" class="text-sm text-gray-500 mb-4px">
                        回复 <span class="text-primary">@{{ comment.replyToUserName }}</span>
                      </div>
                      
                      <!-- 评论内容 -->
                      <div class="my-8px">{{ comment.content }}</div>
                      
                      <!-- 评论操作 -->
                      <div class="flex gap-16px text-sm text-gray-500">
                        <div class="cursor-pointer hover:text-primary" @click="replyToComment(comment)">回复</div>
                        <div class="cursor-pointer hover:text-primary flex items-center gap-2px" @click="handleLikeComment(comment)">
                          <NIcon size="16">
                            <div v-if="comment.isLiked" class="i-material-symbols:favorite text-red-500"></div>
                            <div v-else class="i-material-symbols:favorite-outline"></div>
                          </NIcon>
                          <span>{{ comment.likeCount || '' }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 加载更多评论 -->
                <div v-if="comments.length < commentPagination.total" class="flex justify-center mt-16px">
                  <NButton :loading="commentLoading" @click="loadMoreComments">加载更多</NButton>
                </div>
                <div v-else-if="comments.length > 0" class="text-center text-gray-400 mt-16px">
                  没有更多评论了
                </div>
              </div>
            </NSpin>
          </NCard>
        </div>
        
        <!-- 右侧 - 推荐 -->
        <div class="w-300px">
          <NCard title="相关技能" :bordered="false" class="shadow-sm mb-16px">
            <NEmpty v-if="false" description="暂无相关技能" />
            <NList>
              <NListItem v-for="i in 3" :key="i">
                <div class="flex items-center gap-8px cursor-pointer" @click="router.push(`/skill/${i}`)">
                  <div class="flex-1 truncate">推荐技能{{ i }}</div>
                </div>
              </NListItem>
            </NList>
          </NCard>
          
          <NCard title="热门动态" :bordered="false" class="shadow-sm">
            <NEmpty v-if="false" description="暂无热门动态" />
            <NList>
              <NListItem v-for="i in 5" :key="i">
                <div class="flex items-start gap-8px cursor-pointer" @click="router.push(`/social/post/${i}`)">
                  <div class="flex-1 truncate line-clamp-2 text-sm">热门动态内容示例{{ i }}</div>
                </div>
              </NListItem>
            </NList>
          </NCard>
        </div>
      </div>
      
      <NEmpty v-else description="动态不存在或已删除" />
    </NSpin>
  </div>
</template>

<style scoped>
/* 导入 KaTeX 样式 */
@import 'katex/dist/katex.min.css';

/* Markdown 样式 */
.post-content.markdown-body {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.post-content.markdown-body :deep(h1),
.post-content.markdown-body :deep(h2),
.post-content.markdown-body :deep(h3),
.post-content.markdown-body :deep(h4),
.post-content.markdown-body :deep(h5),
.post-content.markdown-body :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.25;
}

.post-content.markdown-body :deep(h1) { font-size: 20px; }
.post-content.markdown-body :deep(h2) { font-size: 18px; }
.post-content.markdown-body :deep(h3) { font-size: 16px; }

.post-content.markdown-body :deep(p) {
  margin: 8px 0;
}

.post-content.markdown-body :deep(ul),
.post-content.markdown-body :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.post-content.markdown-body :deep(li) {
  margin: 4px 0;
}

.post-content.markdown-body :deep(blockquote) {
  margin: 16px 0;
  padding: 8px 16px;
  border-left: 4px solid #ddd;
  background-color: #f9f9f9;
  color: #666;
}

.post-content.markdown-body :deep(code) {
  background-color: #f1f1f1;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.post-content.markdown-body :deep(pre) {
  background-color: #f8f8f8;
  border: 1px solid #e1e1e8;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  margin: 16px 0;
}

.post-content.markdown-body :deep(pre code) {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

.post-content.markdown-body :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

.post-content.markdown-body :deep(th),
.post-content.markdown-body :deep(td) {
  border: 1px solid #ddd;
  padding: 8px 12px;
  text-align: left;
}

.post-content.markdown-body :deep(th) {
  background-color: #f5f5f5;
  font-weight: 600;
}

.post-content.markdown-body :deep(a) {
  color: #1890ff;
  text-decoration: none;
}

.post-content.markdown-body :deep(a:hover) {
  text-decoration: underline;
}

/* 数学公式样式 */
.post-content.markdown-body :deep(.katex) {
  font-size: 1em;
}

.post-content.markdown-body :deep(.katex-display) {
  margin: 16px 0;
  text-align: center;
}
</style>