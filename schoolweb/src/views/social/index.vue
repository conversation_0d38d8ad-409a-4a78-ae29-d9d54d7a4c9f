<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { fetchPostList, fetchPostDetail, fetchComments, createComment, likePost, likeComment, followUser, createPost, fetchHotPosts, fetchRecommendedUsers } from '@/service/api/social';
import type { Post } from '@/service/api/social';
import { fetchUserInfo } from '@/service/api/user';
import { uploadFile, getFileUrl } from '@/service/api/file';
import { FilePurpose } from '@/enums/filePurpose';
import { useThemeStore } from '@/store/modules/theme';
import { marked } from 'marked';
import hljs from 'highlight.js';
import katex from 'katex';
import { getUserTypeText, getUserTypeColor } from '@/utils/userType';

defineOptions({
  name: 'Social'
});

const router = useRouter();
const themeStore = useThemeStore();

// 配置 marked
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value;
      } catch (err) {
        console.error('Highlight error:', err);
      }
    }
    return hljs.highlightAuto(code).value;
  },
  breaks: true,
  gfm: true
});

// 渲染 Markdown 内容（简化版，用于列表显示）
function renderMarkdownContent(content: string, maxLength = 200): string {
  if (!content) return '';
  try {
    // 截取内容长度
    let truncatedContent = content.length > maxLength ? content.substring(0, maxLength) + '...' : content;

    // 简单的 Markdown 渲染，只处理基本格式
    let html = marked(truncatedContent);

    // 处理数学公式（简化版）
    html = html.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
      try {
        return katex.renderToString(formula.trim(), {
          displayMode: true,
          throwOnError: false
        });
      } catch (error) {
        return match;
      }
    });

    html = html.replace(/\$([^$\n]+?)\$/g, (match, formula) => {
      try {
        return katex.renderToString(formula.trim(), {
          displayMode: false,
          throwOnError: false
        });
      } catch (error) {
        return match;
      }
    });

    return html;
  } catch (error) {
    console.error('Markdown render error:', error);
    return content;
  }
}

// 当前用户信息
const currentUser = ref<{ userId: number; name: string; avatar: string } | null>(null);

// 动态列表
const postList = ref<Post[]>([]);
const loading = ref(false);
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 新建动态
const showCreatePostModal = ref(false);
const newPostForm = reactive({
  content: '',
  images: [] as string[],
  skillId: undefined as number | undefined
});
const uploadImages = ref<any[]>([]);

// 获取用户信息
async function getUserInfo() {
  try {
    const res = await fetchUserInfo();
    if (res && res.userId) {
      currentUser.value = {
        userId: res.userId,
        name: res.name,
        avatar: res.avatar
      };
    }
  } catch (error) {
    console.error('获取用户信息失败', error);
  }
}

// 获取动态列表
async function getPosts(isRefresh = false) {
  if (isRefresh) {
    pagination.page = 1;
  }
  loading.value = true;
  try {
    const res = await fetchPostList({
      page: pagination.page,
      pageSize: pagination.pageSize,
      followed: activeTab.value === 'followed'
    });

    console.log('获取动态列表响应:', res);
    console.log('响应类型:', typeof res);
    console.log('res.data:', res?.data);
    console.log('res.records:', res?.records);

    // 检查不同的响应格式
    let records = null;
    let total = 0;

    if (res && res.records) {
      // 直接包含records的格式
      records = res.records;
      total = res.total || 0;
    } else if (res && res.data && res.data.records) {
      // 嵌套在data中的格式
      records = res.data.records;
      total = res.data.total || 0;
    } else if (Array.isArray(res)) {
      // 直接返回数组的格式
      records = res;
      total = res.length;
    } else if (res && Array.isArray(res.data)) {
      // data是数组的格式
      records = res.data;
      total = res.data.length;
    }

    console.log('解析后的records:', records);
    console.log('解析后的total:', total);

    if (records && Array.isArray(records)) {
      if (isRefresh) {
        postList.value = records;
      } else {
        postList.value = [...postList.value, ...records];
      }
      pagination.total = total;
      console.log('更新后的postList:', postList.value);
    } else {
      console.warn('未找到有效的动态数据');
    }
  } catch (error) {
    console.error('获取动态列表失败', error);
  } finally {
    loading.value = false;
  }
}

// 加载更多
function loadMore() {
  if (postList.value.length < pagination.total) {
    pagination.page += 1;
    getPosts();
  }
}

// 点赞/取消点赞
async function handleLike(post: Post) {
  try {
    await likePost(post.id, !post.isLiked);
    post.isLiked = !post.isLiked;
    post.likeCount = post.isLiked ? post.likeCount + 1 : post.likeCount - 1;
  } catch (error) {
    console.error('操作失败', error);
  }
}

// 关注/取消关注
async function handleFollow(userId: number, index: number) {
  try {
    const isFollowed = postList.value[index].isFollowed;
    await followUser(userId, !isFollowed);
    postList.value[index].isFollowed = !isFollowed;
  } catch (error) {
    console.error('操作失败', error);
  }
}

// 发布动态
async function handleCreatePost() {
  try {
    await createPost({
      content: newPostForm.content,
      images: uploadImages.value.map(item => item.url),
      skillId: newPostForm.skillId
    });
    window.$message?.success('发布成功');
    showCreatePostModal.value = false;
    newPostForm.content = '';
    newPostForm.skillId = undefined;
    uploadImages.value = [];
    getPosts(true);
  } catch (error) {
    console.error('发布失败', error);
  }
}

// 动态详情弹窗相关
const showPostDetailModal = ref(false);
const currentPostDetail = ref<Post | null>(null);
const detailLoading = ref(false);

// 评论相关
const comments = ref<any[]>([]);
const commentLoading = ref(false);
const commentContent = ref('');
const replyTo = ref<any>(null); // 当前回复的评论
const commentPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 热门帖子和推荐用户
const hotPosts = ref<Post[]>([]);
const recommendedUsers = ref<any[]>([]);

// 查看动态详情
async function viewPostDetail(postId: number) {
  console.log('查看动态详情:', postId);
  showPostDetailModal.value = true;
  detailLoading.value = true;
  currentPostDetail.value = null;

  // 重置评论数据
  comments.value = [];
  commentContent.value = '';
  replyTo.value = null;
  commentPagination.page = 1;
  commentPagination.total = 0;

  try {
    // 并行加载动态详情和评论
    const [detailRes, commentsRes] = await Promise.all([
      fetchPostDetail(postId),
      fetchComments(postId, { page: 1, size: commentPagination.pageSize })
    ]);

    console.log('获取动态详情成功:', detailRes.data);
    console.log('获取评论成功:', commentsRes.data);

    currentPostDetail.value = detailRes.data;

    if (commentsRes.data?.records) {
      comments.value = commentsRes.data.records;
      commentPagination.total = commentsRes.data.total;
    }
  } catch (error) {
    console.error('获取动态详情失败:', error);
    window.$message?.error('获取动态详情失败');
    showPostDetailModal.value = false;
  } finally {
    detailLoading.value = false;
  }
}

// 提交评论
async function submitComment() {
  if (!commentContent.value.trim() || !currentPostDetail.value) return;

  try {
    const commentData: any = {
      content: commentContent.value.trim()
    };

    // 如果是回复评论，添加回复信息
    if (replyTo.value) {
      commentData.parentId = replyTo.value.id;
      commentData.replyToUserId = replyTo.value.userId;
    }

    await createComment(currentPostDetail.value.id, commentData);

    window.$message?.success(replyTo.value ? '回复成功' : '评论成功');
    commentContent.value = '';
    replyTo.value = null;

    // 重新加载评论
    const commentsRes = await fetchComments(currentPostDetail.value.id, {
      page: 1,
      size: commentPagination.pageSize
    });

    if (commentsRes.data?.records) {
      comments.value = commentsRes.data.records;
      commentPagination.total = commentsRes.data.total;
      commentPagination.page = 1;
    }

    // 更新动态的评论数
    if (currentPostDetail.value) {
      currentPostDetail.value.commentCount = (currentPostDetail.value.commentCount || 0) + 1;
    }
  } catch (error) {
    console.error('评论失败:', error);
    window.$message?.error('操作失败');
  }
}

// 回复评论
function replyToComment(comment: any) {
  replyTo.value = comment;
  // 聚焦到评论输入框
  setTimeout(() => {
    const commentInput = document.querySelector('#comment-input-modal') as HTMLTextAreaElement;
    if (commentInput) {
      commentInput.focus();
    }
  }, 100);
}

// 取消回复
function cancelReply() {
  replyTo.value = null;
}

// 点赞评论
async function handleLikeComment(comment: any) {
  try {
    await likeComment(comment.id, !comment.isLiked);
    comment.isLiked = !comment.isLiked;
    comment.likeCount = comment.isLiked
      ? (comment.likeCount || 0) + 1
      : Math.max((comment.likeCount || 0) - 1, 0);
  } catch (error) {
    console.error('点赞评论失败:', error);
    window.$message?.error('操作失败');
  }
}

// 获取热门帖子
async function getHotPosts() {
  try {
    const { data } = await fetchHotPosts(5);
    hotPosts.value = data || [];
  } catch (error) {
    console.error('获取热门帖子失败:', error);
  }
}

// 获取推荐用户
async function getRecommendedUsers() {
  try {
    const { data } = await fetchRecommendedUsers(5);
    recommendedUsers.value = data || [];
  } catch (error) {
    console.error('获取推荐用户失败:', error);
  }
}

// 处理推荐用户关注
async function handleFollowUser(user: any) {
  try {
    await followUser(user.userId, !user.isFollowed);
    user.isFollowed = !user.isFollowed;
    user.followersCount = user.isFollowed
      ? user.followersCount + 1
      : Math.max(user.followersCount - 1, 0);
    window.$message?.success(user.isFollowed ? '关注成功' : '取消关注成功');
  } catch (error) {
    console.error('关注操作失败:', error);
    window.$message?.error('操作失败');
  }
}

// 查看用户资料
function viewUserProfile(userId: number) {
  router.push(`/user/${userId}`);
}

// 格式化时间
function formatTime(timeStr: string | number) {
  try {
    const date = new Date(timeStr);
    if (isNaN(date.getTime())) {
      return '时间未知';
    }
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('时间格式化错误:', error);
    return '时间未知';
  }
}

// 切换标签
const activeTab = ref<'all' | 'followed'>('all');
function changeTab(tab: 'all' | 'followed') {
  if (activeTab.value !== tab) {
    activeTab.value = tab;
    getPosts(true);
  }
}

// 自定义上传
async function customRequest(options: any) {
  try {
    const { file, onFinish, onError } = options;
    console.log('上传文件:', file);

    // 从options.file.file获取实际的File对象
    const actualFile = file.file as File;
    const fileUrl = await uploadFile(actualFile, FilePurpose.SOCIAL_POST);
    console.log('上传成功，文件URL:', fileUrl);

    uploadImages.value.push({ name: file.name, url: fileUrl });
    console.log('当前uploadImages:', uploadImages.value);

    onFinish();
    window.$message?.success('图片上传成功');
    return fileUrl;
  } catch (error) {
    console.error('图片上传失败', error);
    window.$message?.error('图片上传失败');
    onError();
    throw error;
  }
}

onMounted(() => {
  getUserInfo();
  getPosts();
  getHotPosts();
  getRecommendedUsers();
});
</script>

<template>
  <div class="p-24px">
    <div class="flex gap-24px">
      <!-- 左侧 - 动态内容 -->
      <div class="flex-1">
        <!-- 发布动态卡片 -->
        <NCard :bordered="false" class="mb-16px shadow-sm">
          <div class="flex gap-16px">
            <NAvatar v-if="currentUser?.avatar" :src="currentUser.avatar" size="large" />
            <NAvatar v-else size="large">{{ currentUser?.name?.substring(0, 1) || '用' }}</NAvatar>
            <div class="flex-1">
              <NInput 
                placeholder="分享你的技能、经验或想法..." 
                type="textarea" 
                :rows="3" 
                @click="showCreatePostModal = true"
              />
              <div class="flex justify-end mt-16px">
                <NButton type="primary" @click="showCreatePostModal = true">
                  <template #icon>
                    <NIcon>
                      <div class="i-material-symbols:edit"></div>
                    </NIcon>
                  </template>
                  发布动态
                </NButton>
              </div>
            </div>
          </div>
        </NCard>
        
        <!-- 标签切换 -->
        <div class="mb-16px">
          <NTabs v-model:value="activeTab" type="segment">
            <NTabPane name="all" tab="全部动态" />
            <NTabPane name="followed" tab="关注的人" />
          </NTabs>
        </div>
        
        <!-- 动态列表 -->
        <div>
          <NScrollbar style="max-height: calc(100vh - 280px);">
            <NList>
              <NEmpty v-if="postList.length === 0" description="暂无动态" />
              <NListItem v-for="(post, index) in postList" :key="post.id">
                <NCard :bordered="false" class="mb-16px shadow-sm">
                  <!-- 用户信息 -->
                  <div class="flex justify-between items-center mb-16px">
                    <div class="flex items-center gap-12px cursor-pointer" @click="viewUserProfile(post.userId)">
                      <NAvatar v-if="post.userAvatar" :src="post.userAvatar" size="medium" />
                      <NAvatar v-else size="medium">{{ post.userName?.substring(0, 1) || '用' }}</NAvatar>
                      <div class="flex-1">
                        <div class="flex items-center gap-8px">
                          <span class="font-medium">{{ post.userName }}</span>
                          <NTag v-if="getUserTypeText(post.userType)" :type="getUserTypeColor(post.userType)" size="small">
                            {{ getUserTypeText(post.userType) }}
                          </NTag>
                        </div>
                        <span class="text-sm text-gray-500">{{ formatTime(post.createdTime) }}</span>
                      </div>
                    </div>
                    <NButton v-if="post.userId !== currentUser?.userId" 
                      :type="post.isFollowed ? 'default' : 'primary'" 
                      size="small"
                      @click="handleFollow(post.userId, index)"
                    >
                      {{ post.isFollowed ? '已关注' : '关注' }}
                    </NButton>
                  </div>
                  
                  <!-- 动态内容 -->
                  <div class="mb-16px cursor-pointer" @click="viewPostDetail(post.id)">
                    <div v-html="renderMarkdownContent(post.content)" class="mb-12px text-base post-content-preview markdown-body"></div>
                    
                    <!-- 图片 -->
                    <div v-if="post.images && post.images.length > 0" class="grid gap-8px image-grid">
                      <div v-for="(img, imgIndex) in post.images" :key="imgIndex" class="image-item">
                        <img :src="getFileUrl(img)" alt="动态图片" class="w-full h-full object-cover rounded-4px" />
                      </div>
                    </div>
                    
                    <!-- 关联技能 -->
                    <NTag v-if="post.skillId" type="info" class="mt-8px" @click.stop="router.push(`/skill/${post.skillId}`)">
                      {{ post.skillTitle }}
                    </NTag>
                  </div>
                  
                  <!-- 交互区 -->
                  <div class="flex text-gray-500">
                    <div class="flex-1 flex items-center gap-4px cursor-pointer hover:text-primary" 
                      @click="handleLike(post)"
                    >
                      <NIcon>
                        <div v-if="post.isLiked" class="i-material-symbols:favorite text-red-500"></div>
                        <div v-else class="i-material-symbols:favorite-outline"></div>
                      </NIcon>
                      <span>{{ post.likeCount || 0 }}</span>
                    </div>
                    
                    <div class="flex-1 flex items-center gap-4px cursor-pointer hover:text-primary"
                      @click="viewPostDetail(post.id)"
                    >
                      <NIcon>
                        <div class="i-material-symbols:comment-outline"></div>
                      </NIcon>
                      <span>{{ post.commentCount || 0 }}</span>
                    </div>
                  </div>
                </NCard>
              </NListItem>
              
              <!-- 加载更多 -->
              <div v-if="postList.length < pagination.total" class="flex justify-center my-16px">
                <NButton :loading="loading" @click="loadMore">加载更多</NButton>
              </div>
              <div v-else-if="postList.length > 0" class="text-center text-gray-400 my-16px">
                没有更多了
              </div>
            </NList>
          </NScrollbar>
        </div>
      </div>
      
      <!-- 右侧 - 推荐 -->
      <div class="w-300px">
        <NCard title="热门帖子" :bordered="false" class="shadow-sm mb-16px">
          <NEmpty v-if="hotPosts.length === 0" description="暂无热门帖子" />
          <NList>
            <NListItem v-for="post in hotPosts" :key="post.id">
              <div class="flex items-start gap-8px cursor-pointer" @click="viewPostDetail(post.id)">
                <div class="flex-1">
                  <div class="text-sm truncate line-clamp-2 mb-4px">{{ post.content }}</div>
                  <div class="flex items-center gap-8px text-xs text-gray-500">
                    <div class="flex items-center gap-2px">
                      <NIcon size="12">
                        <div class="i-material-symbols:favorite text-red-500"></div>
                      </NIcon>
                      <span>{{ post.likeCount || 0 }}</span>
                    </div>
                    <div class="flex items-center gap-2px">
                      <NIcon size="12">
                        <div class="i-material-symbols:comment-outline"></div>
                      </NIcon>
                      <span>{{ post.commentCount || 0 }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </NListItem>
          </NList>
        </NCard>

        <NCard title="推荐用户" :bordered="false" class="shadow-sm">
          <NEmpty v-if="recommendedUsers.length === 0" description="暂无推荐用户" />
          <NList>
            <NListItem v-for="user in recommendedUsers" :key="user.userId">
              <div class="flex items-center gap-12px">
                <NAvatar v-if="user.userAvatar" :src="user.userAvatar" size="small" />
                <NAvatar v-else size="small">{{ user.userName?.substring(0, 1) || '用' }}</NAvatar>
                <div class="flex-1">
                  <div class="flex items-center gap-4px">
                    <span class="text-sm font-medium truncate">{{ user.userName }}</span>
                    <NTag v-if="getUserTypeText(user.userType)" :type="getUserTypeColor(user.userType)" size="tiny">
                      {{ getUserTypeText(user.userType) }}
                    </NTag>
                  </div>
                  <div class="text-xs text-gray-500">{{ user.followersCount }} 关注者</div>
                </div>
                <NButton size="tiny" :type="user.isFollowed ? 'default' : 'primary'" @click="handleFollowUser(user)">
                  {{ user.isFollowed ? '已关注' : '关注' }}
                </NButton>
              </div>
            </NListItem>
          </NList>
        </NCard>
      </div>
    </div>
    
    <!-- 发布动态弹窗 -->
    <NModal v-model:show="showCreatePostModal" preset="card" title="发布动态" style="max-width: 600px;">
      <NForm>
        <NFormItem label="内容">
          <NInput type="textarea" v-model:value="newPostForm.content" :rows="4" placeholder="分享你的技能、经验或想法..." />
        </NFormItem>
        
        <NFormItem label="图片">
          <NUpload
            multiple
            list-type="image-card"
            :max="9"
            accept="image/*"
            :custom-request="customRequest"
            :on-remove="(file) => {
              const index = uploadImages.findIndex(img => img.name === file.name);
              if (index > -1) {
                uploadImages.splice(index, 1);
              }
            }"
          >
            <div style="margin-bottom: 8px;">
              <NIcon size="48" class="text-gray-300">
                <div class="i-material-symbols:cloud-upload"></div>
              </NIcon>
            </div>
            <div>点击或拖拽上传图片</div>
          </NUpload>
        </NFormItem>
        
        <NFormItem label="关联技能">
          <NSelect v-model:value="newPostForm.skillId" placeholder="选择关联的技能服务" clearable />
        </NFormItem>
        
        <div class="flex justify-end gap-12px">
          <NButton @click="showCreatePostModal = false">取消</NButton>
          <NButton type="primary" :disabled="!newPostForm.content" @click="handleCreatePost">
            发布
          </NButton>
        </div>
      </NForm>
    </NModal>

    <!-- 动态详情弹窗 -->
    <NModal v-model:show="showPostDetailModal" preset="card" title="动态详情" style="max-width: 800px; width: 90vw;">
      <NSpin :show="detailLoading">
        <div v-if="currentPostDetail" class="space-y-16px">
          <!-- 用户信息 -->
          <div class="flex items-center gap-12px">
            <NAvatar v-if="currentPostDetail.userAvatar" :src="currentPostDetail.userAvatar" size="large" />
            <NAvatar v-else size="large">{{ currentPostDetail.userName?.substring(0, 1) || '用' }}</NAvatar>
            <div class="flex-1">
              <div class="flex items-center gap-8px">
                <span class="font-medium text-lg">{{ currentPostDetail.userName }}</span>
                <NTag v-if="getUserTypeText(currentPostDetail.userType)" :type="getUserTypeColor(currentPostDetail.userType)" size="small">
                  {{ getUserTypeText(currentPostDetail.userType) }}
                </NTag>
              </div>
              <span class="text-sm text-gray-500">{{ formatTime(currentPostDetail.createdTime) }}</span>
            </div>
          </div>

          <!-- 动态内容 -->
          <div class="border-t border-gray-100 pt-16px">
            <div v-html="renderMarkdownContent(currentPostDetail.content, 1000)" class="mb-16px text-base post-content-detail markdown-body"></div>

            <!-- 图片 -->
            <div v-if="currentPostDetail.images && currentPostDetail.images.length > 0" class="grid gap-12px mb-16px">
              <div v-for="(img, imgIndex) in currentPostDetail.images" :key="imgIndex">
                <img :src="getFileUrl(img)" alt="动态图片" class="max-w-full rounded-4px" />
              </div>
            </div>

            <!-- 关联技能 -->
            <NTag v-if="currentPostDetail.skillId" type="info" class="mb-16px" @click="router.push(`/skill/${currentPostDetail.skillId}`)">
              {{ currentPostDetail.skillTitle }}
            </NTag>
          </div>

          <!-- 交互区 -->
          <div class="flex border-t border-gray-100 pt-16px text-gray-500">
            <div class="flex-1 flex items-center gap-4px cursor-pointer hover:text-primary"
              @click="handleLike(currentPostDetail)"
            >
              <NIcon>
                <div v-if="currentPostDetail.isLiked" class="i-material-symbols:favorite text-red-500"></div>
                <div v-else class="i-material-symbols:favorite-outline"></div>
              </NIcon>
              <span>{{ currentPostDetail.likeCount || 0 }}</span>
            </div>

            <div class="flex-1 flex items-center gap-4px text-gray-500">
              <NIcon>
                <div class="i-material-symbols:comment-outline"></div>
              </NIcon>
              <span>{{ currentPostDetail.commentCount || 0 }}</span>
            </div>
          </div>

          <!-- 评论区 -->
          <div class="border-t border-gray-100 pt-16px mt-16px">
            <h4 class="font-medium mb-12px">评论 ({{ comments.length }})</h4>

            <!-- 评论输入框 -->
            <div class="mb-16px">
              <div v-if="replyTo" class="flex items-center mb-8px text-sm text-gray-500">
                <span>回复 @{{ replyTo.userName }}：</span>
                <NButton text size="tiny" @click="cancelReply">取消</NButton>
              </div>
              <NInput
                id="comment-input-modal"
                v-model:value="commentContent"
                type="textarea"
                :rows="3"
                :placeholder="replyTo ? `回复 @${replyTo.userName}` : '写下你的评论...'"
                class="mb-8px"
              />
              <div class="flex justify-end">
                <NButton
                  type="primary"
                  size="small"
                  :disabled="!commentContent.trim()"
                  @click="submitComment"
                >
                  {{ replyTo ? '发表回复' : '发表评论' }}
                </NButton>
              </div>
            </div>

            <!-- 评论列表 -->
            <div class="space-y-12px max-h-300px overflow-y-auto">
              <NEmpty v-if="comments.length === 0" description="暂无评论" size="small" />
              <div v-else>
                <div v-for="comment in comments" :key="comment.id" class="flex gap-8px py-8px">
                  <NAvatar v-if="comment.userAvatar" :src="comment.userAvatar" size="small" />
                  <NAvatar v-else size="small">{{ comment.userName?.substring(0, 1) || '用' }}</NAvatar>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-baseline gap-6px mb-4px">
                      <span class="font-medium text-sm">{{ comment.userName }}</span>
                      <NTag v-if="getUserTypeText(comment.userType)" :type="getUserTypeColor(comment.userType)" size="tiny">
                        {{ getUserTypeText(comment.userType) }}
                      </NTag>
                      <span class="text-xs text-gray-400">{{ formatTime(comment.createdTime) }}</span>
                    </div>

                    <!-- 回复信息 -->
                    <div v-if="comment.replyToUserName" class="text-xs text-gray-500 mb-4px">
                      回复 <span class="text-primary">@{{ comment.replyToUserName }}</span>
                    </div>

                    <div class="text-sm mb-6px">{{ comment.content }}</div>
                    <div class="flex items-center gap-12px text-xs text-gray-500">
                      <div class="cursor-pointer hover:text-primary" @click="replyToComment(comment)">回复</div>
                      <div class="cursor-pointer hover:text-primary flex items-center gap-2px" @click="handleLikeComment(comment)">
                        <NIcon size="14">
                          <div v-if="comment.isLiked" class="i-material-symbols:favorite text-red-500"></div>
                          <div v-else class="i-material-symbols:favorite-outline"></div>
                        </NIcon>
                        <span>{{ comment.likeCount || '' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="!detailLoading" class="text-center py-32px text-gray-500">
          动态不存在或已删除
        </div>
      </NSpin>
    </NModal>
  </div>
</template>

<style scoped>
/* 导入 KaTeX 样式 */
@import 'katex/dist/katex.min.css';

.image-grid {
  grid-template-columns: repeat(3, 1fr);
}
.image-item {
  aspect-ratio: 1/1;
  overflow: hidden;
}

/* Markdown 预览样式（简化版） */
.post-content-preview.markdown-body {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.post-content-preview.markdown-body :deep(h1),
.post-content-preview.markdown-body :deep(h2),
.post-content-preview.markdown-body :deep(h3),
.post-content-preview.markdown-body :deep(h4),
.post-content-preview.markdown-body :deep(h5),
.post-content-preview.markdown-body :deep(h6) {
  margin: 8px 0 4px 0;
  font-weight: 600;
  line-height: 1.25;
}

.post-content-preview.markdown-body :deep(h1) { font-size: 16px; }
.post-content-preview.markdown-body :deep(h2) { font-size: 15px; }
.post-content-preview.markdown-body :deep(h3) { font-size: 14px; }

.post-content-preview.markdown-body :deep(p) {
  margin: 4px 0;
}

.post-content-preview.markdown-body :deep(ul),
.post-content-preview.markdown-body :deep(ol) {
  margin: 4px 0;
  padding-left: 16px;
}

.post-content-preview.markdown-body :deep(li) {
  margin: 2px 0;
}

.post-content-preview.markdown-body :deep(code) {
  background-color: #f1f1f1;
  padding: 1px 3px;
  border-radius: 2px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.post-content-preview.markdown-body :deep(blockquote) {
  margin: 8px 0;
  padding: 4px 8px;
  border-left: 3px solid #ddd;
  background-color: #f9f9f9;
  color: #666;
}

.post-content-preview.markdown-body :deep(a) {
  color: #1890ff;
  text-decoration: none;
}

.post-content-preview.markdown-body :deep(a:hover) {
  text-decoration: underline;
}

/* 数学公式样式（预览版） */
.post-content-preview.markdown-body :deep(.katex) {
  font-size: 0.9em;
}

.post-content-preview.markdown-body :deep(.katex-display) {
  margin: 8px 0;
  text-align: center;
}

/* 详情弹窗中的 Markdown 样式 */
.post-content-detail.markdown-body {
  font-size: 15px;
  line-height: 1.7;
  color: #333;
}

.post-content-detail.markdown-body :deep(h1),
.post-content-detail.markdown-body :deep(h2),
.post-content-detail.markdown-body :deep(h3),
.post-content-detail.markdown-body :deep(h4),
.post-content-detail.markdown-body :deep(h5),
.post-content-detail.markdown-body :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.25;
}

.post-content-detail.markdown-body :deep(h1) { font-size: 20px; }
.post-content-detail.markdown-body :deep(h2) { font-size: 18px; }
.post-content-detail.markdown-body :deep(h3) { font-size: 16px; }

.post-content-detail.markdown-body :deep(p) {
  margin: 8px 0;
}

.post-content-detail.markdown-body :deep(ul),
.post-content-detail.markdown-body :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.post-content-detail.markdown-body :deep(li) {
  margin: 4px 0;
}

.post-content-detail.markdown-body :deep(code) {
  background-color: #f1f1f1;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.post-content-detail.markdown-body :deep(blockquote) {
  margin: 12px 0;
  padding: 8px 16px;
  border-left: 4px solid #ddd;
  background-color: #f9f9f9;
  color: #666;
}

.post-content-detail.markdown-body :deep(a) {
  color: #1890ff;
  text-decoration: none;
}

.post-content-detail.markdown-body :deep(a:hover) {
  text-decoration: underline;
}

.post-content-detail.markdown-body :deep(.katex) {
  font-size: 1em;
}

.post-content-detail.markdown-body :deep(.katex-display) {
  margin: 12px 0;
  text-align: center;
}
</style>