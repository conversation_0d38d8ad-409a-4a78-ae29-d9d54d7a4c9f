<template>
  <div>
    <div class="mb-16px">
      <h2 class="text-xl font-bold">信用评级管理</h2>
      <p class="text-gray-500 mt-4px">管理所有用户的信用分数和等级</p>
    </div>

    <!-- 搜索和筛选 -->
    <NCard :bordered="false" class="shadow-sm mb-16px">
      <div class="flex flex-wrap gap-16px items-end">
        <div class="flex-1 min-w-200px">
          <NInput
            v-model:value="searchForm.keyword"
            placeholder="搜索用户姓名、学号或邮箱"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <NIcon>
                <div class="i-material-symbols:search"></div>
              </NIcon>
            </template>
          </NInput>
        </div>
        
        <div class="w-120px">
          <NSelect
            v-model:value="searchForm.creditLevel"
            placeholder="信用等级"
            clearable
            :options="creditLevelOptions"
          />
        </div>
        
        <NButton type="primary" @click="handleSearch">
          <template #icon>
            <NIcon>
              <div class="i-material-symbols:search"></div>
            </NIcon>
          </template>
          搜索
        </NButton>
        
        <NButton @click="handleReset">
          <template #icon>
            <NIcon>
              <div class="i-material-symbols:refresh"></div>
            </NIcon>
          </template>
          重置
        </NButton>
      </div>
    </NCard>

    <!-- 数据表格 -->
    <NCard :bordered="false" class="shadow-sm">
      <NDataTable
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: any) => row.userId"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </NCard>

    <!-- 编辑信用评级弹窗 -->
    <NModal v-model:show="showEditModal" preset="card" title="编辑信用评级" style="max-width: 500px;">
      <NForm ref="editFormRef" :model="editForm" :rules="editRules" label-placement="left" label-width="100px">
        <NFormItem label="用户信息">
          <div class="flex items-center gap-12px">
            <NAvatar v-if="editForm.avatar" :src="editForm.avatar" size="medium" />
            <NAvatar v-else size="medium">{{ editForm.userName?.substring(0, 1) || '用' }}</NAvatar>
            <div>
              <div class="font-medium">{{ editForm.userName }}</div>
              <div class="text-sm text-gray-500">{{ editForm.stuId }} | {{ editForm.email }}</div>
            </div>
          </div>
        </NFormItem>
        
        <NFormItem label="当前评级">
          <div class="flex items-center gap-8px">
            <NTag :type="getCreditLevelColor(editForm.creditLevel)" size="medium">
              {{ getCreditLevelText(editForm.creditLevel) }}
            </NTag>
            <span class="text-lg font-bold">{{ editForm.creditScore }}分</span>
          </div>
        </NFormItem>
        
        <NFormItem label="新分数" path="newCreditScore">
          <NInputNumber
            v-model:value="editForm.newCreditScore"
            :min="0"
            :max="100"
            placeholder="请输入新的信用分数"
            class="w-full"
          />
        </NFormItem>
        
        <NFormItem label="预计等级">
          <NTag v-if="editForm.newCreditScore !== null" :type="getCreditLevelColor(getCreditLevelByScore(editForm.newCreditScore).code)" size="medium">
            {{ getCreditLevelText(getCreditLevelByScore(editForm.newCreditScore).code) }}
          </NTag>
          <span v-else class="text-gray-400">请先输入分数</span>
        </NFormItem>
        
        <NFormItem label="变更原因" path="changeReason">
          <NInput
            v-model:value="editForm.changeReason"
            type="textarea"
            :rows="3"
            placeholder="请输入变更原因"
          />
        </NFormItem>
      </NForm>
      
      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="showEditModal = false">取消</NButton>
          <NButton type="primary" :loading="editLoading" @click="handleUpdateCreditRating">确认更新</NButton>
        </div>
      </template>
    </NModal>

    <!-- 历史记录弹窗 -->
    <NModal v-model:show="showHistoryModal" preset="card" title="信用评级历史" style="max-width: 800px;">
      <NDataTable
        :columns="historyColumns"
        :data="historyData"
        :loading="historyLoading"
        :pagination="historyPagination"
        size="small"
        @update:page="handleHistoryPageChange"
      />
    </NModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, h } from 'vue';
import { NButton, NTag, NAvatar, useMessage } from 'naive-ui';
import { fetchCreditRatingList, fetchCreditRatingDetail, updateCreditRating, fetchCreditRatingHistory } from '@/service/api/admin/credit';
import { getCreditLevelByScore, getCreditLevelByCode, getCreditLevelText, getCreditLevelColor, getAllCreditLevels } from '@/utils/creditLevel';
import { getUserTypeText, getUserTypeColor } from '@/utils/userType';

const message = useMessage();

// 搜索表单
const searchForm = reactive({
  keyword: '',
  creditLevel: null as string | null
});

// 表格数据
const tableData = ref<any[]>([]);
const loading = ref(false);

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
});



// 编辑弹窗
const showEditModal = ref(false);
const editLoading = ref(false);
const editFormRef = ref();
const editForm = reactive({
  userId: 0,
  userName: '',
  stuId: '',
  email: '',
  avatar: '',
  creditScore: 0,
  creditLevel: '',
  newCreditScore: null as number | null,
  changeReason: ''
});

// 历史记录弹窗
const showHistoryModal = ref(false);
const historyLoading = ref(false);
const historyData = ref<any[]>([]);
const historyPagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0
});
const currentHistoryUserId = ref<number>(0);

// 信用等级选项
const creditLevelOptions = computed(() => {
  return getAllCreditLevels().map(level => ({
    label: getCreditLevelText(level.code),
    value: level.code
  }));
});



// 表格列定义
const columns = [
  {
    title: '用户信息',
    key: 'userInfo',
    width: 250,
    render: (row: any) => {
      return h('div', { class: 'flex items-center gap-12px' }, [
        h(NAvatar, {
          src: row.avatar,
          size: 'medium'
        }, { default: () => row.userName?.substring(0, 1) || '用' }),
        h('div', [
          h('div', { class: 'font-medium' }, row.userName),
          h('div', { class: 'text-sm text-gray-500' }, row.stuId),
          h('div', { class: 'text-xs text-gray-400' }, row.email)
        ])
      ]);
    }
  },
  {
    title: '用户类型',
    key: 'userType',
    width: 100,
    render: (row: any) => {
      return h(NTag, {
        type: getUserTypeColor(row.userType),
        size: 'small'
      }, { default: () => getUserTypeText(row.userType) });
    }
  },
  {
    title: '信用等级',
    key: 'creditLevel',
    width: 120,
    render: (row: any) => {
      return h(NTag, {
        type: getCreditLevelColor(row.creditLevel),
        size: 'medium'
      }, { default: () => getCreditLevelText(row.creditLevel) });
    }
  },
  {
    title: '信用分数',
    key: 'creditScore',
    width: 120,
    render: (row: any) => {
      return h('span', { class: 'font-bold text-xl text-primary' }, `${row.creditScore}分`);
    }
  },
  {
    title: '更新时间',
    key: 'updatedTime',
    width: 160,
    render: (row: any) => {
      return new Date(row.updatedTime).toLocaleString();
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    render: (row: any) => {
      return h('div', { class: 'flex gap-8px' }, [
        h(NButton, {
          size: 'small',
          type: 'primary',
          onClick: () => handleEdit(row)
        }, { default: () => '编辑' }),
        h(NButton, {
          size: 'small',
          onClick: () => handleViewHistory(row.userId)
        }, { default: () => '历史' })
      ]);
    }
  }
];

// 历史记录表格列
const historyColumns = [
  {
    title: '操作时间',
    key: 'createdTime',
    width: 140,
    render: (row: any) => new Date(row.createdTime).toLocaleString()
  },
  {
    title: '分数变化',
    key: 'scoreChange',
    width: 120,
    render: (row: any) => {
      const change = row.newScore - (row.oldScore || 0);
      const color = change > 0 ? 'success' : change < 0 ? 'error' : 'default';
      return h('span', { class: `text-${color}` }, 
        `${row.oldScore || 0} → ${row.newScore} (${change >= 0 ? '+' : ''}${change})`
      );
    }
  },
  {
    title: '等级变化',
    key: 'levelChange',
    width: 140,
    render: (row: any) => {
      return h('div', { class: 'flex items-center gap-4px' }, [
        h(NTag, { type: getCreditLevelColor(row.oldLevel || 'B'), size: 'tiny' }, 
          { default: () => row.oldLevel || 'B' }),
        h('span', '→'),
        h(NTag, { type: getCreditLevelColor(row.newLevel), size: 'tiny' }, 
          { default: () => row.newLevel })
      ]);
    }
  },
  {
    title: '变更原因',
    key: 'changeReason',
    ellipsis: true
  },
  {
    title: '操作人',
    key: 'operatorName',
    width: 100
  }
];

// 编辑表单验证规则
const editRules = {
  newCreditScore: [
    { required: true, message: '请输入新的信用分数', type: 'number' },
    { type: 'number', min: 0, max: 100, message: '分数必须在0-100之间' }
  ],
  changeReason: [
    { required: true, message: '请输入变更原因' },
    { min: 5, message: '变更原因至少5个字符' }
  ]
};

// 获取数据
async function fetchData() {
  loading.value = true;
  try {
    const { data } = await fetchCreditRatingList({
      keyword: searchForm.keyword || undefined,
      creditLevel: searchForm.creditLevel || undefined,
      page: pagination.page,
      size: pagination.pageSize
    });

    tableData.value = data.records;
    pagination.itemCount = data.total;
  } catch (error) {
    console.error('获取信用评级列表失败:', error);
    message.error('获取数据失败');
  } finally {
    loading.value = false;
  }
}



// 搜索
function handleSearch() {
  pagination.page = 1;
  fetchData();
}

// 重置
function handleReset() {
  searchForm.keyword = '';
  searchForm.creditLevel = null;
  pagination.page = 1;
  fetchData();
}

// 分页变化
function handlePageChange(page: number) {
  pagination.page = page;
  fetchData();
}

function handlePageSizeChange(pageSize: number) {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  fetchData();
}

// 编辑评级
async function handleEdit(row: any) {
  try {
    const { data } = await fetchCreditRatingDetail(row.userId);
    
    Object.assign(editForm, {
      userId: data.userId,
      userName: data.userName,
      stuId: data.stuId,
      email: data.email,
      avatar: data.avatar,
      creditScore: data.creditScore,
      creditLevel: data.creditLevel,
      newCreditScore: data.creditScore,
      changeReason: ''
    });
    
    showEditModal.value = true;
  } catch (error) {
    console.error('获取用户详情失败:', error);
    message.error('获取用户详情失败');
  }
}

// 更新信用评级
async function handleUpdateCreditRating() {
  try {
    await editFormRef.value?.validate();
    
    editLoading.value = true;
    
    await updateCreditRating({
      userId: editForm.userId,
      creditScore: editForm.newCreditScore!,
      changeReason: editForm.changeReason
    });
    
    message.success('更新成功');
    showEditModal.value = false;
    fetchData();
  } catch (error) {
    console.error('更新失败:', error);
    message.error('更新失败');
  } finally {
    editLoading.value = false;
  }
}

// 查看历史记录
async function handleViewHistory(userId: number) {
  currentHistoryUserId.value = userId;
  showHistoryModal.value = true;
  historyLoading.value = true;
  historyPagination.page = 1;
  
  try {
    const { data } = await fetchCreditRatingHistory(userId, 1, 10);
    historyData.value = data.records;
    historyPagination.itemCount = data.total;
  } catch (error) {
    console.error('获取历史记录失败:', error);
    message.error('获取历史记录失败');
  } finally {
    historyLoading.value = false;
  }
}

// 历史记录分页
async function handleHistoryPageChange(page: number) {
  historyPagination.page = page;
  historyLoading.value = true;
  
  try {
    const { data } = await fetchCreditRatingHistory(currentHistoryUserId.value, page, 10);
    historyData.value = data.records;
  } catch (error) {
    console.error('获取历史记录失败:', error);
  } finally {
    historyLoading.value = false;
  }
}

onMounted(() => {
  fetchData();
});
</script>
