<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { fetchMyOrders, payOrder, acceptOrder, rejectOrder, completeOrder, cancelOrder, confirmOrder, rateOrder } from '@/service/api/order';
import { NCard, NTabs, NTabPane, NTag, NButton, NSpace, NAvatar, NEmpty, NSpin } from 'naive-ui';

defineOptions({
  name: 'Order'
});

const router = useRouter();

// 订单类型和状态
enum OrderType {
  BUY = 'buyer',
  SELL = 'seller'
}

enum OrderStatus {
  PENDING_PAYMENT = 0, // 待支付
  PENDING_ACCEPT = 1, // 待接单
  IN_PROGRESS = 2, // 进行中
  PENDING_CONFIRM = 3, // 待确认
  COMPLETED = 4, // 已完成
  CANCELLED = 5, // 已取消
  REJECTED = 6 // 已拒绝
}

// 活动标签页
const activeTab = ref(OrderType.BUY);

// 订单列表
const orders = ref([]);
const loading = ref(false);
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
});

// 获取订单列表
async function getOrders() {
  loading.value = true;
  try {
    const response = await fetchMyOrders({
      type: activeTab.value,
      page: pagination.page,
      size: pagination.size
    });

    if (response && response.data) {
      orders.value = response.data.records || [];
      pagination.total = response.data.total || 0;
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    window.$message?.error('获取订单列表失败');
  } finally {
    loading.value = false;
  }
}

// 当前显示的订单列表
const currentOrders = computed(() => {
  return orders.value;
});

// 订单状态显示
function getStatusText(status: number) {
  const statusMap: Record<number, string> = {
    [OrderStatus.PENDING_PAYMENT]: '待支付',
    [OrderStatus.PENDING_ACCEPT]: '待接单',
    [OrderStatus.IN_PROGRESS]: '进行中',
    [OrderStatus.PENDING_CONFIRM]: '待确认',
    [OrderStatus.COMPLETED]: '已完成',
    [OrderStatus.CANCELLED]: '已取消',
    [OrderStatus.REJECTED]: '已拒绝'
  };
  return statusMap[status] || '未知状态';
}

// 获取订单状态类型
function getStatusType(status: number) {
  const statusTypeMap: Record<number, 'default' | 'info' | 'success' | 'warning' | 'error'> = {
    [OrderStatus.PENDING_PAYMENT]: 'warning',
    [OrderStatus.PENDING_ACCEPT]: 'info',
    [OrderStatus.IN_PROGRESS]: 'info',
    [OrderStatus.PENDING_CONFIRM]: 'warning',
    [OrderStatus.COMPLETED]: 'success',
    [OrderStatus.CANCELLED]: 'default',
    [OrderStatus.REJECTED]: 'error'
  };
  return statusTypeMap[status] || 'default';
}

// 订单操作按钮配置
function getOrderActions(order: any) {
  const actions = [];

  // 买家操作
  if (activeTab.value === OrderType.BUY) {
    switch (order.status) {
      case OrderStatus.PENDING_PAYMENT:
        actions.push({ label: '支付订单', type: 'primary', action: () => handlePayOrder(order.orderId) });
        actions.push({ label: '取消订单', type: 'default', action: () => handleCancelOrder(order.orderId) });
        break;
      case OrderStatus.PENDING_ACCEPT:
        actions.push({ label: '取消订单', type: 'default', action: () => handleCancelOrder(order.orderId) });
        break;
      case OrderStatus.PENDING_CONFIRM:
        actions.push({ label: '确认完成', type: 'success', action: () => handleConfirmOrder(order.orderId) });
        actions.push({ label: '评价订单', type: 'primary', action: () => handleRateOrder(order.orderId) });
        break;
    }
  }
  // 卖家操作
  else {
    switch (order.status) {
      case OrderStatus.PENDING_ACCEPT:
        actions.push({ label: '接受订单', type: 'success', action: () => handleAcceptOrder(order.orderId) });
        actions.push({ label: '拒绝订单', type: 'error', action: () => handleRejectOrder(order.orderId) });
        break;
      case OrderStatus.IN_PROGRESS:
        actions.push({ label: '完成服务', type: 'primary', action: () => handleCompleteOrder(order.orderId) });
        break;
    }
  }

  return actions;
}

// 订单操作方法
async function handlePayOrder(orderId: number) {
  try {
    await payOrder(orderId);
    window.$message?.success('支付成功');
    getOrders(); // 刷新订单列表
  } catch (error: any) {
    window.$message?.error(error.message || '支付失败');
  }
}

async function handleAcceptOrder(orderId: number) {
  try {
    await acceptOrder(orderId);
    window.$message?.success('接单成功');
    getOrders(); // 刷新订单列表
  } catch (error: any) {
    window.$message?.error(error.message || '接单失败');
  }
}

async function handleRejectOrder(orderId: number) {
  try {
    const reason = prompt('请输入拒绝原因：') || '服务提供者拒绝';
    await rejectOrder(orderId, reason);
    window.$message?.success('已拒绝订单');
    getOrders(); // 刷新订单列表
  } catch (error: any) {
    window.$message?.error(error.message || '操作失败');
  }
}

async function handleCompleteOrder(orderId: number) {
  try {
    await completeOrder(orderId);
    window.$message?.success('服务已完成');
    getOrders(); // 刷新订单列表
  } catch (error: any) {
    window.$message?.error(error.message || '操作失败');
  }
}

async function handleCancelOrder(orderId: number) {
  try {
    const reason = prompt('请输入取消原因：') || '用户取消';
    await cancelOrder(orderId, reason);
    window.$message?.success('订单已取消');
    getOrders(); // 刷新订单列表
  } catch (error: any) {
    window.$message?.error(error.message || '取消失败');
  }
}

async function handleConfirmOrder(orderId: number) {
  try {
    // 确认完成订单，将状态从待确认(3)改为已完成(4)
    const confirmed = confirm('确认服务已完成？确认后订单将结束。');
    if (!confirmed) return;

    await confirmOrder(orderId);
    window.$message?.success('订单已确认完成');
    getOrders(); // 刷新订单列表
  } catch (error: any) {
    window.$message?.error(error.message || '操作失败');
  }
}

async function handleRateOrder(orderId: number) {
  try {
    const rating = prompt('请给服务评分（1-5分）：');
    if (!rating || isNaN(Number(rating)) || Number(rating) < 1 || Number(rating) > 5) {
      window.$message?.error('请输入1-5之间的评分');
      return;
    }

    const review = prompt('请输入评价内容（可选）：') || '';

    await rateOrder(orderId, Number(rating), review);
    window.$message?.success('评价成功');
    getOrders(); // 刷新订单列表
  } catch (error: any) {
    window.$message?.error(error.message || '评价失败');
  }
}

// 查看订单详情
function viewOrderDetail(orderId: number) {
  window.$message?.info(`查看订单详情: ${orderId}`);
  // 实际项目中跳转到订单详情页
  // router.push(`/order/${orderId}`);
}

// 标签切换
function handleTabChange() {
  pagination.page = 1;
  getOrders();
}

// 格式化时间
function formatDateTime(dateTime: string) {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString();
}

// 页面初始化
onMounted(() => {
  getOrders();
});
</script>

<template>
  <div class="p-24px">
    <NCard :bordered="false" size="large" class="rounded-16px shadow-sm">
      <template #header>
        <div class="text-18px font-bold">订单管理</div>
      </template>
      
      <NTabs v-model:value="activeTab" type="segment" animated @update:value="handleTabChange">
        <NTabPane name="buyer" tab="我购买的服务">
          <div class="pt-16px">
            <NSpin :show="loading">
              <NEmpty v-if="currentOrders.length === 0 && !loading" description="暂无订单数据" />

              <div v-else class="space-y-16px">
                <NCard
                  v-for="order in currentOrders"
                  :key="order.orderId"
                  :bordered="false"
                  size="small"
                  class="rounded-8px shadow-sm cursor-pointer hover:shadow-md transition-all duration-300"
                  @click="viewOrderDetail(order.orderId)"
                >
                  <div class="flex justify-between">
                    <div class="flex items-center">
                      <span class="text-14px text-gray-500">订单号: {{ order.orderNo }}</span>
                      <span class="mx-8px text-gray-300">|</span>
                      <span class="text-14px text-gray-500">{{ formatDateTime(order.createdTime) }}</span>
                    </div>
                    <NTag :type="getStatusType(order.status)" size="small">
                      {{ getStatusText(order.status) }}
                    </NTag>
                  </div>
                
                  <div class="flex items-center mt-16px">
                    <div class="flex-1">
                      <div class="font-medium text-16px">{{ order.title }}</div>
                      <div class="mt-8px text-14px text-gray-500">
                        <div>服务时间: {{ formatDateTime(order.appointmentTime) }}</div>
                        <div>服务地点: {{ order.location || '线上服务' }}</div>
                        <div v-if="order.buyerNote">备注: {{ order.buyerNote }}</div>
                      </div>
                    </div>

                    <div class="ml-16px flex items-center">
                      <NAvatar
                        round
                        size="small"
                      >
                        {{ order.sellerName?.charAt(0) || 'S' }}
                      </NAvatar>
                      <div class="ml-8px">
                        <div class="text-14px">{{ order.sellerName || '服务提供者' }}</div>
                        <div class="text-12px text-gray-400">提供者</div>
                      </div>
                    </div>
                  </div>

                  <div class="flex justify-between items-center mt-16px pt-16px border-t border-gray-100">
                    <div>
                      <span class="text-14px text-gray-500">服务单价: {{ order.price }} 学币</span>
                      <span class="mx-8px text-gray-300">×</span>
                      <span class="text-14px text-gray-500">{{ order.studentCount }} 小时</span>
                    </div>
                    <div class="flex items-center">
                      <span class="mr-16px">
                        <span class="text-gray-500">合计: </span>
                        <span class="text-16px font-medium text-primary">{{ order.totalAmount }}</span>
                        <span class="text-gray-500"> 学币</span>
                      </span>

                      <NSpace v-if="getOrderActions(order).length > 0">
                        <NButton
                          v-for="(action, idx) in getOrderActions(order)"
                          :key="idx"
                          :type="action.type"
                          size="small"
                          @click.stop="action.action()"
                        >
                          {{ action.label }}
                        </NButton>
                      </NSpace>
                    </div>
                  </div>
                </NCard>
              </div>
            </NSpin>
          </div>
        </NTabPane>

        <NTabPane name="seller" tab="我提供的服务">
          <div class="pt-16px">
            <NSpin :show="loading">
              <NEmpty v-if="currentOrders.length === 0 && !loading" description="暂无订单数据" />

              <div v-else class="space-y-16px">
                <NCard
                  v-for="order in currentOrders"
                  :key="order.orderId"
                  :bordered="false"
                  size="small"
                  class="rounded-8px shadow-sm cursor-pointer hover:shadow-md transition-all duration-300"
                  @click="viewOrderDetail(order.orderId)"
                >
                  <div class="flex justify-between">
                    <div class="flex items-center">
                      <span class="text-14px text-gray-500">订单号: {{ order.orderNo }}</span>
                      <span class="mx-8px text-gray-300">|</span>
                      <span class="text-14px text-gray-500">{{ formatDateTime(order.createdTime) }}</span>
                    </div>
                    <NTag :type="getStatusType(order.status)" size="small">
                      {{ getStatusText(order.status) }}
                    </NTag>
                  </div>

                  <div class="flex items-center mt-16px">
                    <div class="flex-1">
                      <div class="font-medium text-16px">{{ order.title }}</div>
                      <div class="mt-8px text-14px text-gray-500">
                        <div>服务时间: {{ formatDateTime(order.appointmentTime) }}</div>
                        <div>服务地点: {{ order.location || '线上服务' }}</div>
                        <div v-if="order.buyerNote">客户需求: {{ order.buyerNote }}</div>
                      </div>
                    </div>

                    <div class="ml-16px flex items-center">
                      <NAvatar
                        round
                        size="small"
                      >
                        {{ order.buyerName?.charAt(0) || 'B' }}
                      </NAvatar>
                      <div class="ml-8px">
                        <div class="text-14px">{{ order.buyerName || '客户' }}</div>
                        <div class="text-12px text-gray-400">客户</div>
                      </div>
                    </div>
                  </div>

                  <div class="flex justify-between items-center mt-16px pt-16px border-t border-gray-100">
                    <div>
                      <span class="text-14px text-gray-500">服务单价: {{ order.price }} 学币</span>
                      <span class="mx-8px text-gray-300">×</span>
                      <span class="text-14px text-gray-500">{{ order.studentCount }} 小时</span>
                    </div>
                    <div class="flex items-center">
                      <span class="mr-16px">
                        <span class="text-gray-500">合计: </span>
                        <span class="text-16px font-medium text-primary">{{ order.totalAmount }}</span>
                        <span class="text-gray-500"> 学币</span>
                      </span>

                      <NSpace v-if="getOrderActions(order).length > 0">
                        <NButton
                          v-for="(action, idx) in getOrderActions(order)"
                          :key="idx"
                          :type="action.type"
                          size="small"
                          @click.stop="action.action()"
                        >
                          {{ action.label }}
                        </NButton>
                      </NSpace>
                    </div>
                  </div>
                </NCard>
              </div>
            </NSpin>
          </div>
        </NTabPane>
      </NTabs>
    </NCard>
  </div>
</template>

<style scoped></style>