import { request } from '../request';
import type { ApiResponse } from '../types';

export interface Post {
  id: number;
  userId: number;
  userName?: string;
  userAvatar?: string;
  userType?: number; // 用户类型：1-学生，2-教师，3-管理员
  content: string;
  images?: string[];
  likeCount: number;
  commentCount: number;
  isLiked?: boolean;
  isFollowed?: boolean;
  createdTime: string;
  skillId?: number;
  skillTitle?: string;
}

export interface Comment {
  id: number;
  postId: number;
  userId: number;
  userName?: string;
  userAvatar?: string;
  userType?: number; // 用户类型：1-学生，2-教师，3-管理员
  content: string;
  likeCount: number;
  isLiked?: boolean;
  createdTime: string;
  parentId?: number;
  replyToUserId?: number;
  replyToUserName?: string;
}

export interface PostSearchParams {
  userId?: number;
  keyword?: string;
  followed?: boolean;
  page: number;
  size: number;
}

export interface PostListResult {
  records: Post[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

export interface CommentListResult {
  records: Comment[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 获取动态列表
 */
export function fetchPostList(params: PostSearchParams) {
  return request<ApiResponse<PostListResult>>({
    url: '/posts/list',
    method: 'get',
    params
  });
}

/**
 * 获取动态详情
 */
export function fetchPostDetail(id: number) {
  return request<ApiResponse<Post>>({
    url: `/posts/${id}`,
    method: 'get'
  });
}

/**
 * 创建动态
 */
export function createPost(data: {
  content: string;
  images?: string[];
  skillId?: number;
}) {
  return request<ApiResponse<string>>({
    url: '/posts/create',
    method: 'post',
    data
  });
}

/**
 * 删除动态
 */
export function deletePost(id: number) {
  return request<ApiResponse<string>>({
    url: `/posts/${id}`,
    method: 'delete'
  });
}

/**
 * 点赞/取消点赞动态
 */
export function likePost(id: number, like: boolean) {
  return request<ApiResponse<string>>({
    url: `/posts/${id}/like`,
    method: 'post',
    data: { like }
  });
}

/**
 * 获取动态评论列表
 */
export function fetchComments(postId: number, params: {
  page: number;
  size: number;
}) {
  return request<ApiResponse<CommentListResult>>({
    url: `/posts/${postId}/comments`,
    method: 'get',
    params
  });
}

/**
 * 创建评论
 */
export function createComment(postId: number, data: {
  content: string;
  parentId?: number;
  replyToUserId?: number;
}) {
  return request<ApiResponse<string>>({
    url: `/posts/${postId}/comments`,
    method: 'post',
    data
  });
}

/**
 * 删除评论
 */
export function deleteComment(commentId: number) {
  return request<ApiResponse<string>>({
    url: `/comments/${commentId}`,
    method: 'delete'
  });
}

/**
 * 点赞/取消点赞评论
 */
export function likeComment(id: number, like: boolean) {
  return request<ApiResponse<string>>({
    url: `/comments/${id}/like`,
    method: 'post',
    data: { like }
  });
}

/**
 * 关注/取消关注用户
 */
export function followUser(userId: number, follow: boolean) {
  return request<ApiResponse<string>>({
    url: `/users/${userId}/follow`,
    method: 'post',
    data: { follow }
  });
}

/**
 * 获取关注列表
 */
export function fetchFollowings(userId: number, params: {
  page: number;
  size: number;
}) {
  return request<ApiResponse<{
    records: { id: number; name: string; avatar: string; isFollowed: boolean }[];
    total: number;
    size: number;
    current: number;
    pages: number;
  }>>({
    url: `/users/${userId}/followings`,
    method: 'get',
    params
  });
}

/**
 * 获取粉丝列表
 */
export function fetchFollowers(userId: number, params: {
  page: number;
  size: number;
}) {
  return request<ApiResponse<{
    records: { id: number; name: string; avatar: string; isFollowed: boolean }[];
    total: number;
    size: number;
    current: number;
    pages: number;
  }>>({
    url: `/users/${userId}/followers`,
    method: 'get',
    params
  });
}

/**
 * 获取热门帖子（按点赞量排序）
 */
export function fetchHotPosts(limit = 5) {
  return request<ApiResponse<Post[]>>({
    url: '/social/posts/hot',
    method: 'get',
    params: { limit }
  });
}

/**
 * 获取推荐用户（按关注量排序）
 */
export function fetchRecommendedUsers(limit = 5) {
  return request<ApiResponse<{
    userId: number;
    userName: string;
    userAvatar?: string;
    userType: number;
    followersCount: number;
    isFollowed: boolean;
  }[]>>({
    url: '/social/users/recommended',
    method: 'get',
    params: { limit }
  });
}