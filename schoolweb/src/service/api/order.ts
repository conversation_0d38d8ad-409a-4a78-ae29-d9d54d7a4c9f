import { request } from '../request';
import type { ApiResponse } from '../types';

export interface Order {
  id: number;
  orderNo: string;
  skillId: number;
  skillTitle?: string;
  skillPrice?: number;
  skillUnit?: string;
  quantity: number;
  totalAmount: number;
  consumerId: number;
  consumerName?: string;
  consumerAvatar?: string;
  providerId: number;
  providerName?: string;
  providerAvatar?: string;
  status: number; // 0-待支付，1-待接单，2-进行中，3-待确认，4-已完成，5-已取消，6-已拒绝
  createdTime: string;
  updatedTime: string;
  appointmentTime?: string; // 预约时间
  appointmentAddress?: string; // 预约地点
  appointmentDuration?: number; // 预约时长（小时）
  appointmentRemark?: string; // 预约备注
  refundReason?: string; // 退款原因
  rejectReason?: string; // 拒绝原因
  rating?: number; // 评分
  review?: string; // 评价内容
  reviewTime?: string; // 评价时间
}

export interface OrderSearchParams {
  status?: number;
  keyword?: string;
  startTime?: string;
  endTime?: string;
  type?: string; // 查询类型：buyer/seller
  page: number;
  size: number;
}

export interface OrderListResult {
  records: Order[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 创建订单
 */
export function createOrder(data: any) {
  return request<ApiResponse<{ orderId: number; orderNo: string; totalAmount: number }>>({
    url: '/order/create',
    method: 'post',
    data
  });
}

/**
 * 获取订单详情
 */
export function fetchOrderDetail(orderId: number) {
  return request<ApiResponse<Order>>({
    url: `/order/${orderId}`,
    method: 'get'
  });
}

/**
 * 获取我的订单列表
 */
export function fetchMyOrders(params: OrderSearchParams) {
  return request<ApiResponse<OrderListResult>>({
    url: '/order/my',
    method: 'get',
    params
  });
}

/**
 * 确认订单
 */
export function confirmOrder(orderId: number) {
  return request<ApiResponse<string>>({
    url: `/order/${orderId}/confirm`,
    method: 'post'
  });
}

/**
 * 完成订单
 */
export function completeOrder(orderId: number) {
  return request<ApiResponse<string>>({
    url: `/order/${orderId}/complete`,
    method: 'post'
  });
}

/**
 * 取消订单
 */
export function cancelOrder(orderId: number, reason: string) {
  return request<ApiResponse<string>>({
    url: `/order/${orderId}/cancel`,
    method: 'post',
    data: { cancelReason: reason }
  });
}

/**
 * 评价订单
 */
export function rateOrder(orderId: number, rating: number, review: string) {
  return request<ApiResponse<string>>({
    url: `/order/${orderId}/rate`,
    method: 'post',
    data: { rating, review }
  });
}

/**
 * 支付订单
 */
export function payOrder(orderId: number) {
  return request<ApiResponse<string>>({
    url: `/order/${orderId}/pay`,
    method: 'post'
  });
}

/**
 * 接受订单
 */
export function acceptOrder(orderId: number) {
  return request<ApiResponse<string>>({
    url: `/order/${orderId}/accept`,
    method: 'post'
  });
}

/**
 * 拒绝订单
 */
export function rejectOrder(orderId: number, reason: string) {
  return request<ApiResponse<string>>({
    url: `/order/${orderId}/reject`,
    method: 'post',
    data: { rejectReason: reason }
  });
}
