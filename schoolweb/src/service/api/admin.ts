import { request } from '../request';
import type { ApiResponse } from '../types';
import type { UserInfo } from './user';
import type { SkillService } from './skill';

export interface DashboardData {
  userCount: number;
  skillCount: number;
  orderCount: number;
  orderAmount: number;
  newUserTrend: { date: string; count: number }[];
  newOrderTrend: { date: string; count: number }[];
  categoryDistribution: { category: string; count: number }[];
  orderStatusDistribution: { status: number; statusName: string; count: number }[];
}

/**
 * 审核项类型
 */
export interface AuditItem {
  auditId: number;
  targetId: number;
  targetType: string;
  targetName: string;
  status: number;
  submitTime: string;
  auditTime?: string;
  auditorId?: number;
  auditorName?: string;
  reason?: string;
  content: string;
}

/**
 * 审核列表查询参数
 */
export interface AuditSearchParams {
  page: number;
  pageSize: number;
  type?: string;
  status?: number;
  keyword?: string;
  startTime?: string | null;
  endTime?: string | null;
}

/**
 * 审核列表结果
 */
export interface AuditListResult {
  records: AuditItem[];
  total: number;
  pages: number;
  current: number;
  size: number;
}

/**
 * 获取管理后台首页数据
 */
export function fetchDashboardData(timeRange: string) {
  return request<ApiResponse<DashboardData>>({
    url: '/admin/dashboard',
    method: 'get',
    params: { timeRange }
  });
}

/**
 * 获取审核列表
 * @param params 查询参数
 */
export function fetchAuditList(params: AuditSearchParams) {
  return request<AuditListResult>({
    url: '/admin/audits',
    method: 'get',
    params,
    config: {
      // 不自动显示错误消息
      noShowDefaultError: true
    }
  });
}

/**
 * 审核操作
 * @param id 审核ID
 * @param data 审核数据
 */
export function auditItem(id: number, data: { status: number; reason?: string }) {
  // 状态码对应AuditStatus枚举：1-通过(APPROVED)，2-拒绝(REJECTED)
  return request({
    url: `/admin/audit/${id}`,
    method: 'post',
    data,
    config: {
      // 不自动显示错误消息
      noShowDefaultError: true
    }
  });
}

/**
 * 获取用户管理列表
 */
export function fetchAdminUserList(params: {
  role?: string;
  status?: number;
  keyword?: string;
  page: number;
  size: number;
}) {
  return request<ApiResponse<{
    records: UserInfo[];
    total: number;
    size: number;
    current: number;
    pages: number;
  }>>({
    url: '/admin/users',
    method: 'get',
    params
  });
}

/**
 * 获取技能服务管理列表
 */
export function fetchAdminSkillList(params: {
  categoryId?: number;
  status?: number;
  keyword?: string;
  page: number;
  size: number;
}) {
  return request<ApiResponse<{
    records: SkillService[];
    total: number;
    size: number;
    current: number;
    pages: number;
  }>>({
    url: '/admin/skills',
    method: 'get',
    params
  });
}

/**
 * 更改用户状态
 */
export function changeUserStatus(userId: number, status: number, reason?: string) {
  return request<ApiResponse<string>>({
    url: `/admin/user/${userId}/status`,
    method: 'post',
    data: { status, reason }
  });
}

/**
 * 更改技能服务状态
 */
export function changeSkillStatus(skillId: number, status: number, reason?: string) {
  return request<ApiResponse<string>>({
    url: `/admin/skill/${skillId}/status`,
    method: 'post',
    data: { status, reason }
  });
}

/**
 * 获取系统操作日志
 */
export function fetchOperationLogs(params: {
  operatorId?: number;
  operationType?: string;
  startTime?: string;
  endTime?: string;
  page: number;
  size: number;
}) {
  return request<ApiResponse<{
    records: {
      id: number;
      operatorId: number;
      operatorName: string;
      operationType: string;
      operationDesc: string;
      ip: string;
      createdTime: string;
    }[];
    total: number;
    size: number;
    current: number;
    pages: number;
  }>>({
    url: '/admin/logs',
    method: 'get',
    params
  });
}

/**
 * 导出用户数据
 */
export function exportUsers(params: {
  role?: string;
  status?: number;
  keyword?: string;
}) {
  return request({
    url: '/admin/export/users',
    method: 'get',
    params,
    responseType: 'blob' as 'json'
  });
}

/**
 * 导出技能服务数据
 */
export function exportSkills(params: {
  categoryId?: number;
  status?: number;
  keyword?: string;
}) {
  return request({
    url: '/admin/export/skills',
    method: 'get',
    params,
    responseType: 'blob' as 'json'
  });
}

/**
 * 获取审核详情
 * @param id 审核ID
 */
export function fetchAuditDetail(id: number) {
  return request<any>({
    url: `/admin/audit/${id}/detail`,
    method: 'get'
  });
}

/**
 * 创建系统备份
 */
export function createSystemBackup() {
  return request<ApiResponse<string>>({
    url: '/admin/backup/create',
    method: 'post',
    timeout: 300000 // 5分钟超时
  });
}

/**
 * 导出数据库
 */
export function exportDatabase() {
  return request<ApiResponse<string>>({
    url: '/admin/backup/database',
    method: 'post',
    timeout: 180000 // 3分钟超时
  });
}

/**
 * 打包上传文件
 */
export function packUploadFiles() {
  return request<ApiResponse<string>>({
    url: '/admin/backup/files',
    method: 'post',
    timeout: 240000 // 4分钟超时
  });
}