import { request } from '../request';
import type { ApiResponse } from './user';
import { localStg } from '@/utils/storage';
import { FilePurpose } from '@/enums/filePurpose';

/**
 * 上传文件
 * @param file 文件对象
 * @param purpose 文件用途
 */
export function uploadFile(file: File, purpose: FilePurpose = FilePurpose.OTHER) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('purpose', purpose);

  // 添加调试日志
  console.log('上传文件:', file);
  console.log('文件用途:', purpose);
  console.log('FormData内容:');
  for (let [key, value] of formData.entries()) {
    console.log(key, value);
  }

  return request<any>({
    url: '/upload',
    method: 'POST',
    headers: {
      'Content-Type': undefined // 删除默认的Content-Type，让浏览器自动设置multipart/form-data
    },
    data: formData
  }).then(res => {
    console.log('上传响应完整数据:', res);

    // 使用createFlatRequest，响应格式为 { data, error }
    if (res.error) {
      console.error('请求错误:', res.error);
      throw new Error(res.error.message || '上传失败');
    }

    if (res.data) {
      // createFlatRequest会自动处理后端响应，res.data就是后端返回的data字段
      // 后端返回格式：{ code: 200, msg: "文件上传成功", data: "/files/..." }
      // createFlatRequest处理后：res.data = "/files/..."
      console.log('文件路径:', res.data);
      return res.data;
    }

    throw new Error('上传失败：无响应数据');
  });
}

/**
 * 获取文件URL
 * @param fileUrl 文件路径
 */
export function getFileUrl(fileUrl: string) {
  if (!fileUrl) return '';
  // 如果已经是完整URL，直接返回
  if (fileUrl.startsWith('http')) {
    return fileUrl;
  }
  const baseUrl = import.meta.env.VITE_SERVICE_BASE_URL;
  // 确保路径以/开头
  const path = fileUrl.startsWith('/') ? fileUrl : `/${fileUrl}`;
  // 文件访问不需要token，因为已经在后端配置了白名单
  return `${baseUrl}${path}`;
}