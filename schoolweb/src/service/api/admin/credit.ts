import { request } from '../../request';

/**
 * 管理员信用评级相关接口
 */

// 信用评级列表查询参数
export interface CreditRatingQuery {
  keyword?: string;
  creditLevel?: string;
  userType?: number;
  page?: number;
  size?: number;
}

// 信用评级更新参数
export interface CreditRatingUpdate {
  userId: number;
  creditScore: number;
  changeReason: string;
}

// 批量更新参数
export interface BatchCreditUpdate {
  updates: CreditRatingUpdate[];
}

// 用户信用信息
export interface UserCreditInfo {
  userId: number;
  userName: string;
  stuId: string;
  academy: string;
  userType: number;
  email: string;
  phone: string;
  creditScore: number;
  creditLevel: string;
  status: number;
  createdTime: string;
  updatedTime: string;
}

// 信用评级历史记录
export interface CreditRatingHistoryRecord {
  historyId: number;
  userId: number;
  userName: string;
  stuId: string;
  userType: number;
  oldScore: number;
  newScore: number;
  oldLevel: string;
  newLevel: string;
  changeReason: string;
  operationType: string;
  operatorId: number;
  operatorName: string;
  createdTime: string;
}

/**
 * 分页查询用户信用评级列表
 */
export function fetchCreditRatingList(params: CreditRatingQuery) {
  return request<ApiResponse<{
    records: UserCreditInfo[];
    total: number;
    size: number;
    current: number;
    pages: number;
  }>>({
    url: '/admin/credit/list',
    method: 'get',
    params
  });
}

/**
 * 查询用户信用评级详情
 */
export function fetchCreditRatingDetail(userId: number) {
  return request<ApiResponse<UserCreditInfo>>({
    url: `/admin/credit/detail/${userId}`,
    method: 'get'
  });
}

/**
 * 更新用户信用评级
 */
export function updateCreditRating(data: CreditRatingUpdate) {
  return request<ApiResponse<any>>({
    url: '/admin/credit/update',
    method: 'post',
    data
  });
}

/**
 * 查询用户信用评级历史记录
 */
export function fetchCreditRatingHistory(userId: number, page = 1, size = 20) {
  return request<ApiResponse<{
    records: CreditRatingHistoryRecord[];
    total: number;
    size: number;
    current: number;
    pages: number;
  }>>({
    url: `/admin/credit/history/${userId}`,
    method: 'get',
    params: { page, size }
  });
}

/**
 * 查询最近的评级变更记录
 */
export function fetchRecentChanges(limit = 10) {
  return request<ApiResponse<CreditRatingHistoryRecord[]>>({
    url: '/admin/credit/recent-changes',
    method: 'get',
    params: { limit }
  });
}

/**
 * 查询信用等级统计
 */
export function fetchCreditLevelStatistics() {
  return request<ApiResponse<{
    level: string;
    count: number;
    percentage: number;
  }[]>>({
    url: '/admin/credit/statistics/level',
    method: 'get'
  });
}

/**
 * 查询信用分数分布统计
 */
export function fetchCreditScoreDistribution() {
  return request<ApiResponse<{
    scoreRange: string;
    count: number;
  }[]>>({
    url: '/admin/credit/statistics/score',
    method: 'get'
  });
}

/**
 * 批量更新信用评级
 */
export function batchUpdateCreditRating(data: BatchCreditUpdate) {
  return request<ApiResponse<any>>({
    url: '/admin/credit/batch-update',
    method: 'post',
    data
  });
}
