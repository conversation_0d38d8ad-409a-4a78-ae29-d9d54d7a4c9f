# 校园技能共享平台开发文档

## 1. 项目概述

校园技能共享平台是一个基于Spring Boot + Vue3的前后端分离项目，旨在为校园师生提供技能交流和服务的平台。

### 1.1 项目结构

```
schoolskill/                                    # 项目根目录
├── src/                                        # 后端源码目录
│   ├── main/                                   # 主要源码
│   │   ├── java/com/school/                    # Java源码包
│   │   │   ├── Main.java                       # 主启动类
│   │   │   ├── annotation/                     # 自定义注解
│   │   │   ├── aspect/                         # AOP切面
│   │   │   ├── config/                         # 配置类
│   │   │   ├── controller/                     # 控制器层
│   │   │   │   ├── admin/                      # 管理员控制器
│   │   │   │   ├── order/                      # 订单控制器
│   │   │   │   ├── skill/                      # 技能服务控制器
│   │   │   │   ├── social/                     # 社交功能控制器
│   │   │   │   ├── user/                       # 用户控制器
│   │   │   │   └── wallet/                     # 钱包控制器
│   │   │   ├── entity/                         # 实体类
│   │   │   ├── enums/                          # 枚举类
│   │   │   ├── exception/                      # 异常处理
│   │   │   ├── mapper/                         # 数据访问层
│   │   │   ├── service/                        # 服务层
│   │   │   │   └── impl/                       # 服务实现类
│   │   │   └── util/                           # 工具类
│   │   └── resources/                          # 资源文件
│   │       ├── application.yml                 # 主配置文件
│   │       ├── application-dev.yml             # 开发环境配置
│   │       ├── application-prod.yml            # 生产环境配置
│   │       ├── mapper/                         # MyBatis映射文件
│   │       └── templates/                      # 模板文件
│   └── test/                                   # 测试代码
│       └── java/                               # 测试Java代码
├── schoolweb/                                  # 前端项目目录
│   ├── src/                                    # Vue3源码
│   │   ├── App.vue                             # 根组件
│   │   ├── main.ts                             # 入口文件
│   │   ├── api/                                # API接口（旧版本）
│   │   ├── assets/                             # 静态资源
│   │   │   ├── imgs/                           # 图片资源
│   │   │   └── svg-icon/                       # SVG图标
│   │   ├── components/                         # 公共组件
│   │   │   ├── advanced/                       # 高级组件
│   │   │   ├── common/                         # 通用组件
│   │   │   ├── custom/                         # 自定义组件
│   │   │   └── lpx-timetable/                  # 时间表组件
│   │   ├── constants/                          # 常量定义
│   │   ├── enum/                               # 枚举（旧版本）
│   │   ├── enums/                              # 枚举定义
│   │   ├── hooks/                              # Vue3 Hooks
│   │   │   ├── business/                       # 业务相关Hooks
│   │   │   └── common/                         # 通用Hooks
│   │   ├── layouts/                            # 布局组件
│   │   │   ├── base-layout/                    # 基础布局
│   │   │   ├── blank-layout/                   # 空白布局
│   │   │   ├── context/                        # 布局上下文
│   │   │   └── modules/                        # 布局模块
│   │   ├── locales/                            # 国际化
│   │   │   └── langs/                          # 语言包
│   │   ├── plugins/                            # 插件配置
│   │   ├── router/                             # 路由配置
│   │   │   ├── elegant/                        # 优雅路由
│   │   │   ├── guard/                          # 路由守卫
│   │   │   └── routes/                         # 路由定义
│   │   ├── service/                            # API服务
│   │   │   ├── api/                            # API接口定义
│   │   │   └── request/                        # 请求封装
│   │   ├── store/                              # 状态管理
│   │   │   ├── modules/                        # 状态模块
│   │   │   └── plugins/                        # 状态插件
│   │   ├── styles/                             # 样式文件
│   │   │   ├── css/                            # CSS样式
│   │   │   └── scss/                           # SCSS样式
│   │   ├── theme/                              # 主题配置
│   │   ├── typings/                            # TypeScript类型定义
│   │   ├── utils/                              # 工具函数
│   │   │   └── storage/                        # 存储工具
│   │   └── views/                              # 页面组件
│   │       ├── _builtin/                       # 内置页面
│   │       ├── admin/                          # 管理员页面
│   │       ├── admin-credit/                   # 管理员信用页面
│   │       ├── home/                           # 首页
│   │       ├── logManagement/                  # 日志管理
│   │       ├── order/                          # 订单页面
│   │       ├── profile/                        # 个人资料
│   │       ├── roleManagement/                 # 角色管理
│   │       ├── skill/                          # 技能服务页面
│   │       ├── skill-detail/                   # 技能详情页面
│   │       ├── skill-publish/                  # 技能发布页面
│   │       ├── social/                         # 社交页面
│   │       ├── user-credit/                    # 用户信用页面
│   │       ├── userManagement/                 # 用户管理
│   │       └── wallet/                         # 钱包页面
│   ├── public/                                 # 公共静态资源
│   ├── build/                                  # 构建输出目录
│   ├── node_modules/                           # 前端依赖包
│   ├── package.json                            # 前端依赖配置
│   ├── pnpm-lock.yaml                          # pnpm锁定文件
│   ├── vite.config.ts                          # Vite配置
│   ├── tsconfig.json                           # TypeScript配置
│   ├── uno.config.ts                           # UnoCSS配置
│   ├── eslint.config.js                        # ESLint配置
│   └── index.html                              # HTML入口文件
├── target/                                     # Maven构建输出
│   ├── classes/                                # 编译后的class文件
│   ├── generated-sources/                      # 生成的源码
│   ├── maven-archiver/                         # Maven归档信息
│   ├── maven-status/                           # Maven状态
│   ├── surefire-reports/                       # 测试报告
│   ├── test-classes/                           # 测试class文件
│   └── CodeAcademyManageSystem-1.0.jar         # 打包后的jar文件
├── mysql/                                      # 数据库脚本
│   └── school_skill_share.sql                  # 数据库初始化脚本
├── docs/                                       # 项目文档
│   ├── 系统设计文档.md                         # 系统设计文档
│   ├── 系统开发文档.md                         # 系统开发文档
│   └── 技术难点解析文档.md                     # 技术难点解析文档
├── logs/                                       # 日志文件目录
│   └── application.log                         # 应用日志
├── upload/                                     # 文件上传目录
├── pom.xml                                     # Maven项目配置
└── CodeAcademyManageSystem.iml                 # IntelliJ IDEA项目文件
```

## 2. 环境准备

### 2.1 开发环境要求

#### 开发环境（Windows）
- **Java**: OpenJDK 21 或 Oracle JDK 21
- **Maven**: 3.6.0+
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Node.js**: 18.0+
- **IDE**: IntelliJ IDEA 2023+ (统一使用IDEA开发前后端)

### 2.2 环境安装（Windows）

#### 2.2.1 Java环境安装

1. **下载安装JDK 21**
   - 访问 https://adoptium.net/
   - 下载Windows x64版本的OpenJDK 21
   - 运行安装程序，默认安装到 `C:\Program Files\Eclipse Adoptium\jdk-21.x.x.x-hotspot\`

2. **配置环境变量**
   ```
   右键"此电脑" → 属性 → 高级系统设置 → 环境变量

   新建系统变量：
   变量名：JAVA_HOME
   变量值：C:\Program Files\Eclipse Adoptium\jdk-21.x.x.x-hotspot

   编辑Path变量，添加：
   %JAVA_HOME%\bin
   ```

3. **验证安装**
   ```cmd
   java -version
   javac -version
   ```

#### 2.2.2 Maven安装和换源

1. **下载安装Maven**
   - 访问 https://maven.apache.org/download.cgi
   - 下载Binary zip archive版本
   - 解压到 `C:\Program Files\Apache\maven-3.x.x\`

2. **配置环境变量**
   ```
   新建系统变量：
   变量名：MAVEN_HOME
   变量值：C:\Program Files\Apache\maven-3.x.x

   编辑Path变量，添加：
   %MAVEN_HOME%\bin
   ```

3. **Maven换源配置**
   编辑 `%MAVEN_HOME%\conf\settings.xml` 文件，在 `<mirrors>` 标签内添加：
   ```xml
   <mirror>
     <id>aliyunmaven</id>
     <mirrorOf>*</mirrorOf>
     <name>阿里云公共仓库</name>
     <url>https://maven.aliyun.com/repository/public</url>
   </mirror>
   ```

4. **验证安装**
   ```cmd
   mvn -version
   ```

#### 2.2.3 MySQL安装配置

1. **下载安装MySQL 8.0**
   - 访问 https://dev.mysql.com/downloads/mysql/
   - 下载Windows (x86, 64-bit), MSI Installer版本
   - 运行安装程序，选择"Developer Default"安装类型

2. **配置MySQL**
   - 设置root用户密码（建议：root123456）
   - 选择"Use Strong Password Encryption"
   - 配置Windows服务，设置为开机自启

3. **创建项目数据库**
   打开MySQL Command Line Client，执行：
   ```sql
   -- 创建数据库
   CREATE DATABASE school_skill_share CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

   -- 创建用户
   CREATE USER 'school_user'@'localhost' IDENTIFIED BY 'school_password';

   -- 授权
   GRANT ALL PRIVILEGES ON school_skill_share.* TO 'school_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

#### 2.2.4 Redis安装

1. **下载Redis for Windows**
   - 访问 https://github.com/tporadowski/redis/releases
   - 下载最新版本的Redis-x64-x.x.x.msi
   - 运行安装程序，默认安装即可

2. **启动Redis服务**
   - 安装完成后Redis会自动作为Windows服务启动
   - 可通过"服务"管理器查看Redis服务状态
   - 默认端口：6379

#### 2.2.5 Node.js安装和换源

1. **下载安装Node.js**
   - 访问 https://nodejs.org/
   - 下载LTS版本（推荐18.x）
   - 运行安装程序，勾选"Add to PATH"选项

2. **验证安装**
   ```cmd
   node -v
   npm -v
   ```

3. **npm换源配置**
   ```cmd
   # 设置淘宝镜像源
   npm config set registry https://registry.npmmirror.com

   # 验证源设置
   npm config get registry

   # 安装pnpm
   npm install -g pnpm

   # pnpm换源
   pnpm config set registry https://registry.npmmirror.com
   ```

## 3. 项目启动

### 3.1 数据库初始化

1. 确保MySQL服务运行
2. 导入数据库脚本:
```bash
mysql -u school_user -p school_skill_share < mysql/school_skill_share.sql
```

### 3.2 后端启动

#### 3.2.1 配置文件修改

编辑 `src/main/resources/application.yml`:

```yaml
spring:
  datasource:
    url: ******************************************************************************************************************
    username: school_user
    password: school_password
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  redis:
    host: localhost
    port: 6379
    password: # Redis密码，如果没有设置则留空
    database: 0
    
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

server:
  port: 8080

# Sa-Token配置
sa-token:
  token-name: satoken
  timeout: 2592000
  activity-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false
```

#### 3.2.2 启动后端服务

**使用IDE启动:**
1. 导入项目到IntelliJ IDEA
2. 等待Maven依赖下载完成
3. 运行主类 `com.school.CodeAcademyManageSystemApplication`

**使用命令行启动:**
```bash
# 在项目根目录执行
mvn clean compile
mvn spring-boot:run

# 或者打包后运行
mvn clean package
java -jar target/CodeAcademyManageSystem-1.0.jar
```

**验证后端启动:**
访问 http://localhost:8080/api/test 查看是否返回正常响应

### 3.3 前端启动（使用IntelliJ IDEA）

#### 3.3.1 在IDEA中打开前端项目

1. **打开前端项目**
   - 在IDEA中选择 File → Open
   - 选择项目根目录下的 `schoolweb` 文件夹
   - 等待IDEA识别为Node.js项目

2. **安装依赖**
   在IDEA底部Terminal中执行：
   ```cmd
   pnpm install
   ```

#### 3.3.2 配置环境变量

在 `schoolweb` 目录下创建 `.env.development` 文件：
```env
# 开发环境配置
VITE_APP_TITLE=校园技能共享平台
VITE_APP_BASE_API=http://localhost:8080/api
VITE_APP_UPLOAD_URL=http://localhost:8080/api/file/upload
```

#### 3.3.3 在IDEA中启动前端服务

1. **方法一：使用npm scripts**
   - 打开 `package.json` 文件
   - 在 `scripts` 部分找到 `dev` 脚本
   - 点击脚本旁边的绿色运行按钮

2. **方法二：使用Terminal**
   在IDEA Terminal中执行：
   ```cmd
   pnpm dev
   ```

3. **验证前端启动**
   访问 http://localhost:5173 查看前端页面

## 4. IntelliJ IDEA开发配置

### 4.1 必要插件安装

1. **打开插件管理**
   File → Settings → Plugins

2. **安装以下插件**
   - **Lombok Plugin** - 支持Lombok注解
   - **MyBatis X** - MyBatis增强工具
   - **Vue.js** - Vue.js开发支持
   - **GitToolBox** - Git增强工具
   - **Chinese Language Pack** - 中文语言包（可选）

### 4.2 项目配置

#### 4.2.1 导入项目
1. File → Open → 选择项目根目录
2. 等待Maven依赖下载完成
3. 确认Project SDK设置为JDK 21

#### 4.2.2 代码格式化配置
1. File → Settings → Editor → Code Style
2. 设置Java代码格式：
   - Indent: 4 spaces
   - Continuation indent: 8 spaces
   - Tab size: 4
3. 设置自动格式化：
   - File → Settings → Tools → Actions on Save
   - 勾选 "Reformat code" 和 "Optimize imports"

#### 4.2.3 Maven配置
1. File → Settings → Build → Build Tools → Maven
2. 设置Maven home directory为安装路径
3. 设置User settings file为配置的settings.xml
4. 勾选 "Import Maven projects automatically"

### 4.3 运行配置

#### 4.3.1 后端运行配置
1. 找到主类 `CodeAcademyManageSystemApplication`
2. 右键 → Run 'CodeAcademyManageSystemApplication'
3. 或点击类旁边的绿色运行按钮

#### 4.3.2 前端运行配置
1. 打开 `schoolweb/package.json`
2. 在 scripts 的 dev 脚本旁点击运行按钮
3. 或在Terminal中执行 `pnpm dev`

## 5. 常见问题解决

### 5.1 后端启动问题

**问题1: 数据库连接失败**
```
解决方案:
1. 检查MySQL服务是否启动（服务管理器中查看MySQL80服务）
2. 验证application.yml中数据库连接配置
3. 确认数据库用户权限和密码
4. 检查3306端口是否被占用
```

**问题2: Redis连接失败**
```
解决方案:
1. 检查Redis服务是否启动（服务管理器中查看Redis服务）
2. 验证Redis配置（默认端口6379）
3. 如果设置了密码，检查application.yml中的密码配置
```

**问题3: 端口占用**
```
解决方案:
1. 查看端口占用: netstat -ano | findstr :8080
2. 杀死占用进程: taskkill /PID <进程ID> /F
3. 或修改application.yml中的server.port配置
```

**问题4: Maven依赖下载失败**
```
解决方案:
1. 检查网络连接
2. 确认Maven镜像源配置正确
3. 删除.m2/repository目录重新下载
4. 在IDEA中刷新Maven项目
```

### 5.2 前端启动问题

**问题1: 依赖安装失败**
```
解决方案:
1. 检查npm源配置: npm config get registry
2. 清除缓存: pnpm store prune
3. 删除node_modules和pnpm-lock.yaml重新安装
4. 使用管理员权限运行命令行
```

**问题2: 编译错误**
```
解决方案:
1. 检查Node.js版本: node -v (需要18+)
2. 检查TypeScript语法错误
3. 清除缓存: pnpm store prune
4. 重新安装依赖: pnpm install
```

**问题3: 端口占用**
```
解决方案:
1. 查看端口占用: netstat -ano | findstr :5173
2. 杀死占用进程或修改vite.config.ts中的端口配置
3. 使用其他端口: pnpm dev --port 3000
```

### 5.3 IDEA配置问题

**问题1: 项目无法识别为Maven项目**
```
解决方案:
1. 确保pom.xml文件存在且格式正确
2. File → Project Structure → Modules → 添加Maven模块
3. 右键pom.xml → Add as Maven Project
```

**问题2: JDK版本问题**
```
解决方案:
1. File → Project Structure → Project → Project SDK设置为JDK 21
2. File → Settings → Build → Build Tools → Maven → Runner → JRE设置为JDK 21
3. 检查JAVA_HOME环境变量
```

## 6. 开发规范

### 6.1 代码规范

#### 6.1.1 Java代码规范
- 使用驼峰命名法
- 类名首字母大写（如：UserController）
- 方法名和变量名首字母小写（如：getUserInfo）
- 常量全大写，下划线分隔（如：MAX_SIZE）
- 注释使用JavaDoc格式
- 每个方法都要有注释说明

#### 6.1.2 Vue代码规范
- 组件名使用PascalCase（如：UserProfile.vue）
- 文件名使用kebab-case（如：user-profile.vue）
- 变量名使用camelCase（如：userName）
- 常量使用UPPER_SNAKE_CASE（如：API_BASE_URL）
- 使用TypeScript类型注解

### 6.2 Git提交规范

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动

示例:
feat: 添加用户登录功能
fix: 修复订单支付bug
docs: 更新API文档
```

### 6.3 项目结构说明

#### 6.3.1 后端结构说明
```
src/main/java/com/school/
├── Main.java                    # 应用启动类
├── annotation/                  # 自定义注解（如@OperationLog）
├── aspect/                      # AOP切面（日志记录、权限验证等）
├── config/                      # 配置类（Redis、Sa-Token、跨域等）
├── controller/                  # 控制器层
│   ├── admin/                   # 管理员相关接口
│   ├── order/                   # 订单管理接口
│   ├── skill/                   # 技能服务接口
│   ├── social/                  # 社交功能接口
│   ├── user/                    # 用户管理接口
│   └── wallet/                  # 钱包管理接口
├── entity/                      # 实体类（对应数据库表）
├── enums/                       # 枚举类（状态码、用户类型等）
├── exception/                   # 异常处理类
├── mapper/                      # MyBatis数据访问层
├── service/                     # 服务层接口
│   └── impl/                    # 服务层实现类
└── util/                        # 工具类
```

#### 6.3.2 前端结构说明
```
schoolweb/src/
├── views/                       # 页面组件
│   ├── home/                    # 首页
│   ├── skill/                   # 技能服务列表
│   ├── skill-detail/            # 技能服务详情
│   ├── skill-publish/           # 技能发布
│   ├── order/                   # 订单管理
│   ├── wallet/                  # 钱包管理
│   ├── social/                  # 社交功能
│   ├── profile/                 # 个人资料
│   ├── admin/                   # 管理员页面
│   ├── userManagement/          # 用户管理
│   ├── roleManagement/          # 角色管理
│   ├── logManagement/           # 日志管理
│   ├── admin-credit/            # 管理员信用管理
│   └── user-credit/             # 用户信用页面
├── components/                  # 公共组件
│   ├── common/                  # 通用组件
│   ├── advanced/                # 高级组件
│   └── custom/                  # 自定义组件
├── service/api/                 # API接口定义
├── store/modules/               # Pinia状态管理模块
├── router/                      # 路由配置
├── utils/                       # 工具函数
├── hooks/                       # Vue3 Hooks
├── layouts/                     # 布局组件
└── typings/                     # TypeScript类型定义
```

#### 6.3.3 重要文件说明
- **pom.xml**: Maven项目配置，定义依赖和构建配置
- **application.yml**: Spring Boot主配置文件
- **package.json**: 前端项目依赖和脚本配置
- **vite.config.ts**: Vite构建工具配置
- **school_skill_share.sql**: 数据库初始化脚本

---

*本文档提供了完整的Windows环境下项目开发环境搭建和启动指南，统一使用IntelliJ IDEA进行前后端开发，帮助开发者快速上手项目开发。*
