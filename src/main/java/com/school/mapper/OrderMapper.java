package com.school.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.school.entity.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单Mapper接口
 */
@Mapper
public interface OrderMapper extends BaseMapper<Order> {
    
    /**
     * 根据订单号查询订单
     */
    @Select("SELECT * FROM orders WHERE order_no = #{orderNo}")
    Order selectByOrderNo(@Param("orderNo") String orderNo);
    
    /**
     * 分页查询用户订单
     */
    @Select("<script>" +
            "SELECT o.* " +
            "FROM orders o " +
            "WHERE 1=1 " +
            "<if test='userId != null'>" +
            "  <if test='type == \"buyer\"'> AND o.buyer_id = #{userId} </if>" +
            "  <if test='type == \"seller\"'> AND o.seller_id = #{userId} </if>" +
            "</if>" +
            "<if test='status != null'> AND o.status = #{status} </if>" +
            "<if test='keyword != null and keyword != \"\"'> AND (o.order_no LIKE CONCAT('%', #{keyword}, '%') OR o.title LIKE CONCAT('%', #{keyword}, '%')) </if>" +
            "<if test='startTime != null'> AND o.created_time >= #{startTime} </if>" +
            "<if test='endTime != null'> AND o.created_time &lt;= #{endTime} </if>" +
            "ORDER BY o.created_time DESC" +
            "</script>")
    IPage<Order> selectUserOrders(Page<Order> page,
                                 @Param("userId") Integer userId,
                                 @Param("type") String type,
                                 @Param("status") Integer status,
                                 @Param("keyword") String keyword,
                                 @Param("startTime") Date startTime,
                                 @Param("endTime") Date endTime);
    
    /**
     * 更新订单状态
     */
    @Update("UPDATE orders SET status = #{status}, updated_time = NOW() " +
            "WHERE order_id = #{orderId}")
    int updateOrderStatus(@Param("orderId") Long orderId,
                         @Param("status") Integer status,
                         @Param("version") Integer version);

    /**
     * 支付订单
     */
    @Update("UPDATE orders SET status = #{status}, payment_time = #{payTime}, " +
            "updated_time = NOW() " +
            "WHERE order_id = #{orderId} AND status = 0")
    int payOrder(@Param("orderId") Long orderId,
                @Param("status") Integer status,
                @Param("payTime") Date payTime,
                @Param("version") Integer version);

    /**
     * 接单
     */
    @Update("UPDATE orders SET status = #{status}, confirm_time = #{acceptTime}, " +
            "updated_time = NOW() " +
            "WHERE order_id = #{orderId} AND status = 1")
    int acceptOrder(@Param("orderId") Long orderId,
                   @Param("status") Integer status,
                   @Param("acceptTime") Date acceptTime,
                   @Param("version") Integer version);
    
    /**
     * 拒绝订单
     */
    @Update("UPDATE orders SET status = #{status}, seller_note = #{rejectReason}, " +
            "updated_time = NOW() " +
            "WHERE order_id = #{orderId} AND status = 1")
    int rejectOrder(@Param("orderId") Long orderId,
                   @Param("status") Integer status,
                   @Param("rejectReason") String rejectReason,
                   @Param("version") Integer version);

    /**
     * 完成订单
     */
    @Update("UPDATE orders SET status = #{status}, complete_time = #{completeTime}, " +
            "updated_time = NOW() " +
            "WHERE order_id = #{orderId} AND status = 2")
    int completeOrder(@Param("orderId") Long orderId,
                     @Param("status") Integer status,
                     @Param("completeTime") Date completeTime,
                     @Param("version") Integer version);

    /**
     * 取消订单
     */
    @Update("UPDATE orders SET status = #{status}, cancel_time = #{cancelTime}, " +
            "cancel_reason = #{cancelReason}, updated_time = NOW() " +
            "WHERE order_id = #{orderId} AND status IN (0, 1)")
    int cancelOrder(@Param("orderId") Long orderId,
                   @Param("status") Integer status,
                   @Param("cancelTime") Date cancelTime,
                   @Param("cancelReason") String cancelReason,
                   @Param("version") Integer version);

    /**
     * 评价订单
     */
    @Update("UPDATE orders SET seller_note = CONCAT(IFNULL(seller_note, ''), '\\n评分:', #{rating}, '\\n评价:', #{review}), " +
            "updated_time = NOW() " +
            "WHERE order_id = #{orderId} AND status = 4")
    int rateOrder(@Param("orderId") Long orderId,
                 @Param("rating") Integer rating,
                 @Param("review") String review,
                 @Param("reviewTime") Date reviewTime,
                 @Param("version") Integer version);
    
    /**
     * 统计用户订单数量
     */
    @Select("SELECT COUNT(*) FROM orders WHERE buyer_id = #{userId} AND status = #{status}")
    Integer countUserOrdersByStatus(@Param("userId") Integer userId, @Param("status") Integer status);

    /**
     * 检查用户是否已预约该技能服务（防止重复预约）
     */
    @Select("SELECT COUNT(*) FROM orders WHERE buyer_id = #{userId} AND service_id = #{skillId} " +
            "AND status IN (0, 1, 2) AND appointment_time = #{appointmentTime}")
    Integer checkDuplicateAppointment(@Param("userId") Integer userId,
                                    @Param("skillId") Long skillId,
                                    @Param("appointmentTime") Date appointmentTime);

    /**
     * 获取订单总金额（成交量）
     */
    @Select("SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE status IN (3, 4)")
    BigDecimal getTotalOrderAmount();
}
