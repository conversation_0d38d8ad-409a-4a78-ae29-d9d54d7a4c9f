package com.school.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.school.entity.SysLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface SysLogMapper extends BaseMapper<SysLog> {

    /**
     * 获取每日访问量趋势数据
     */
    @Select("SELECT DATE(operation_time) as date, COUNT(*) as count " +
            "FROM sys_log " +
            "WHERE operation_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY) " +
            "GROUP BY DATE(operation_time) " +
            "ORDER BY date ASC")
    List<Map<String, Object>> getDailyVisitTrend(@Param("days") int days);
}