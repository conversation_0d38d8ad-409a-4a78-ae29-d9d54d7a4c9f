package com.school.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.util.SaResult;
import com.school.annotation.OperationLog;
import com.school.service.OrderService;
import com.school.service.SkillServiceService;
import com.school.service.SysLogService;
import com.school.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 首页控制器
 */
@RestController
@RequestMapping("/home")
@RequiredArgsConstructor
public class HomeController {

    private final UserService userService;
    private final SkillServiceService skillServiceService;
    private final OrderService orderService;
    private final SysLogService sysLogService;

    /**
     * 获取首页统计数据
     */
    @GetMapping("/statistics")
    @SaCheckRole("superAdmin")
    @OperationLog(operationModule = "首页", operationType = "查询", operationDesc = "查询首页统计数据")
    public SaResult getHomeStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 获取用户总数
            long userCount = userService.count();
            statistics.put("userCount", userCount);
            
            // 获取技能服务总数
            long skillCount = skillServiceService.count();
            statistics.put("skillCount", skillCount);
            
            // 获取订单总数
            long orderCount = orderService.count();
            statistics.put("orderCount", orderCount);
            
            // 获取订单总金额（成交量）
            BigDecimal orderAmount = orderService.getTotalOrderAmount();
            statistics.put("orderAmount", orderAmount != null ? orderAmount : BigDecimal.ZERO);
            
            // 获取访问量（日志总数）
            long visitCount = sysLogService.count();
            statistics.put("visitCount", visitCount);
            
            return SaResult.ok("首页统计数据查询成功").setData(statistics);
        } catch (Exception e) {
            return SaResult.error("首页统计数据查询失败: " + e.getMessage());
        }
    }
}
