package com.school.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.school.entity.Order;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单服务接口
 */
public interface OrderService {
    
    /**
     * 创建订单（预约服务）
     */
    Order createOrder(Integer userId, Long skillId, Integer quantity, 
                     Date appointmentTime, String appointmentAddress, 
                     Integer appointmentDuration, String appointmentRemark);
    
    /**
     * 支付订单
     */
    boolean payOrder(Long orderId, Integer userId);
    
    /**
     * 接受订单
     */
    boolean acceptOrder(Long orderId, Integer userId);
    
    /**
     * 拒绝订单
     */
    boolean rejectOrder(Long orderId, Integer userId, String rejectReason);
    
    /**
     * 完成订单
     */
    boolean completeOrder(Long orderId, Integer userId);
    
    /**
     * 取消订单
     */
    boolean cancelOrder(Long orderId, Integer userId, String cancelReason);
    
    /**
     * 确认订单完成
     */
    boolean confirmOrder(Long orderId, Integer userId);

    /**
     * 评价订单
     */
    boolean rateOrder(Long orderId, Integer userId, Integer rating, String review);
    
    /**
     * 获取订单详情
     */
    Order getOrderDetail(Long orderId, Integer userId);
    
    /**
     * 分页查询用户订单
     */
    IPage<Order> getUserOrders(Integer userId, String type, Integer page, Integer size,
                              Integer status, String keyword, Date startTime, Date endTime);
    
    /**
     * 检查订单权限
     */
    boolean checkOrderPermission(Long orderId, Integer userId, String operation);
    
    /**
     * 检查是否可以预约（防止重复预约）
     */
    boolean canMakeAppointment(Integer userId, Long skillId, Date appointmentTime);

    /**
     * 获取订单总金额（成交量）
     */
    BigDecimal getTotalOrderAmount();

    /**
     * 获取订单总数
     */
    long count();
}
