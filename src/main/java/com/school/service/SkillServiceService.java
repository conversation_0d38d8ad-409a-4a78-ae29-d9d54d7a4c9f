package com.school.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.school.entity.SkillPublishRequest;
import com.school.entity.SkillServiceDTO;

import java.math.BigDecimal;

/**
 * 技能服务业务接口
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
public interface SkillServiceService {
    
    /**
     * 分页查询技能服务列表
     */
    IPage<SkillServiceDTO> getSkillServicePage(
            Integer page, 
            Integer size, 
            String keyword, 
            Integer categoryId, 
            BigDecimal priceMin, 
            BigDecimal priceMax, 
            String sortBy
    );
    
    /**
     * 根据ID获取技能服务详情
     */
    SkillServiceDTO getSkillServiceDetail(Long serviceId);
    
    /**
     * 发布技能服务
     */
    Long publishSkillService(Integer userId, SkillPublishRequest request);
    
    /**
     * 更新技能服务
     */
    boolean updateSkillService(Long serviceId, Integer userId, SkillPublishRequest request);
    
    /**
     * 删除技能服务
     */
    boolean deleteSkillService(Long serviceId, Integer userId);
    
    /**
     * 获取用户发布的技能服务列表
     */
    IPage<SkillServiceDTO> getUserSkillServices(Integer userId, Integer page, Integer size, Integer status);

    /**
     * 更新技能服务状态
     */
    boolean updateSkillServiceStatus(Long serviceId, Integer status, String reason);
    
    /**
     * 增加浏览次数
     */
    void incrementViewCount(Long serviceId);
    
    /**
     * 检查用户是否有权限操作该服务
     */
    boolean checkUserPermission(Long serviceId, Integer userId);

    /**
     * 获取技能服务总数
     */
    long count();
}
