package com.school.service;

/**
 * 系统备份服务接口
 * 
 * <AUTHOR>
 * @since 2025-03-02
 */
public interface SystemBackupService {
    
    /**
     * 创建系统备份
     * 包括数据库导出和文件打包
     * 
     * @return 备份文件的下载路径
     * @throws Exception 备份过程中的异常
     */
    String createSystemBackup() throws Exception;
    
    /**
     * 导出数据库为SQL文件
     * 
     * @return SQL文件路径
     * @throws Exception 导出过程中的异常
     */
    String exportDatabase() throws Exception;
    
    /**
     * 打包上传文件夹为压缩包
     * 
     * @return 压缩包文件路径
     * @throws Exception 打包过程中的异常
     */
    String packUploadFiles() throws Exception;
}
