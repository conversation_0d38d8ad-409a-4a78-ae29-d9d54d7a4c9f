package com.school.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.school.entity.SysLog;
import com.school.mapper.SysLogMapper;
import com.school.service.SysLogService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 系统日志服务实现类
 */
@Service
public class SysLogServiceImpl extends ServiceImpl<SysLogMapper, SysLog> implements SysLogService {

    @Override
    public List<Map<String, Object>> getDailyVisitTrend(int days) {
        return baseMapper.getDailyVisitTrend(days);
    }
}
