package com.school.service.impl;

import com.school.service.SystemBackupService;
import com.school.service.FileUploadService;
import com.school.enums.FilePurposeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 系统备份服务实现类
 * 
 * <AUTHOR>
 * @since 2025-03-02
 */
@Slf4j
@Service
public class SystemBackupServiceImpl implements SystemBackupService {
    
    @Autowired
    private FileUploadService fileUploadService;
    
    @Value("${spring.datasource.url}")
    private String databaseUrl;
    
    @Value("${spring.datasource.username}")
    private String databaseUsername;
    
    @Value("${spring.datasource.password}")
    private String databasePassword;
    
    @Value("${file.upload.path}")
    private String uploadPath;
    
    @Override
    public String createSystemBackup() throws Exception {
        log.info("开始创建系统备份...");
        
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String tempDir = System.getProperty("java.io.tmpdir") + "/backup_" + timestamp;
        
        try {
            // 创建临时目录
            Files.createDirectories(Paths.get(tempDir));
            
            // 导出数据库
            String sqlFile = exportDatabaseToFile(tempDir, timestamp);
            log.info("数据库导出完成: {}", sqlFile);
            
            // 打包上传文件
            String filesArchive = packUploadFilesToArchive(tempDir, timestamp);
            log.info("文件打包完成: {}", filesArchive);
            
            // 创建最终的备份压缩包
            String finalBackupPath = createFinalBackup(tempDir, timestamp);
            log.info("系统备份创建完成: {}", finalBackupPath);
            
            return finalBackupPath;
            
        } finally {
            // 清理临时目录
            deleteDirectory(Paths.get(tempDir));
        }
    }
    
    @Override
    public String exportDatabase() throws Exception {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String tempDir = System.getProperty("java.io.tmpdir") + "/db_export_" + timestamp;
        
        try {
            Files.createDirectories(Paths.get(tempDir));
            return exportDatabaseToFile(tempDir, timestamp);
        } finally {
            // 注意：这里不删除临时目录，因为文件还需要被下载
        }
    }
    
    @Override
    public String packUploadFiles() throws Exception {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String tempDir = System.getProperty("java.io.tmpdir") + "/files_pack_" + timestamp;
        
        try {
            Files.createDirectories(Paths.get(tempDir));
            return packUploadFilesToArchive(tempDir, timestamp);
        } finally {
            // 注意：这里不删除临时目录，因为文件还需要被下载
        }
    }
    
    /**
     * 导出数据库到SQL文件
     */
    private String exportDatabaseToFile(String outputDir, String timestamp) throws Exception {
        // 从数据库URL中提取数据库名
        String dbName = extractDatabaseName(databaseUrl);
        String sqlFileName = "database_backup_" + timestamp + ".sql";
        String sqlFilePath = outputDir + "/" + sqlFileName;
        
        // 构建mysqldump命令
        ProcessBuilder processBuilder = new ProcessBuilder(
            "mysqldump",
            "--host=" + extractHost(databaseUrl),
            "--port=" + extractPort(databaseUrl),
            "--user=" + databaseUsername,
            "--password=" + databasePassword,
            "--single-transaction",
            "--routines",
            "--triggers",
            "--all-databases"
        );
        
        processBuilder.redirectOutput(new File(sqlFilePath));
        processBuilder.redirectErrorStream(true);
        
        Process process = processBuilder.start();
        int exitCode = process.waitFor();
        
        if (exitCode != 0) {
            throw new RuntimeException("数据库导出失败，退出码: " + exitCode);
        }
        
        // 将SQL文件上传到文件系统
        byte[] sqlBytes = Files.readAllBytes(Paths.get(sqlFilePath));
        MockMultipartFile sqlFile = new MockMultipartFile(
            "file",
            "database_" + timestamp + ".sql",
            "application/sql",
            sqlBytes
        );
        
        return fileUploadService.saveFile(sqlFile, FilePurposeEnum.SYSTEM_BACKUP);
    }
    
    /**
     * 打包上传文件到压缩包
     */
    private String packUploadFilesToArchive(String outputDir, String timestamp) throws Exception {
        String archiveFileName = "upload_files_" + timestamp + ".zip";
        String archivePath = outputDir + "/" + archiveFileName;

        // 创建ZIP压缩包
        try (FileOutputStream fos = new FileOutputStream(archivePath);
             ZipOutputStream zos = new ZipOutputStream(fos)) {

            Path uploadDir = Paths.get(uploadPath);
            if (Files.exists(uploadDir)) {
                addDirectoryToZip(uploadDir, uploadDir, zos);
            }
        }

        // 将压缩包上传到文件系统
        byte[] archiveBytes = Files.readAllBytes(Paths.get(archivePath));
        MockMultipartFile archiveFile = new MockMultipartFile(
            "file",
            "files_" + timestamp + ".zip",
            "application/zip",
            archiveBytes
        );

        return fileUploadService.saveFile(archiveFile, FilePurposeEnum.SYSTEM_BACKUP);
    }
    
    /**
     * 创建最终的备份压缩包
     */
    private String createFinalBackup(String tempDir, String timestamp) throws Exception {
        String finalBackupName = "system_backup_" + timestamp + ".zip";
        String finalBackupPath = tempDir + "/" + finalBackupName;
        
        try (FileOutputStream fos = new FileOutputStream(finalBackupPath);
             ZipOutputStream zos = new ZipOutputStream(fos)) {
            
            // 添加临时目录中的所有文件到最终压缩包
            Path tempDirPath = Paths.get(tempDir);
            Files.walk(tempDirPath)
                .filter(Files::isRegularFile)
                .filter(path -> !path.toString().endsWith(finalBackupName))
                .forEach(path -> {
                    try {
                        String entryName = tempDirPath.relativize(path).toString();
                        ZipEntry entry = new ZipEntry(entryName);
                        zos.putNextEntry(entry);
                        Files.copy(path, zos);
                        zos.closeEntry();
                    } catch (IOException e) {
                        log.error("添加文件到压缩包失败: {}", path, e);
                    }
                });
        }
        
        // 将最终备份文件上传到文件系统
        byte[] backupBytes = Files.readAllBytes(Paths.get(finalBackupPath));
        MockMultipartFile backupFile = new MockMultipartFile(
            "file",
            "system_backup_" + timestamp + ".zip",
            "application/zip",
            backupBytes
        );
        
        return fileUploadService.saveFile(backupFile, FilePurposeEnum.SYSTEM_BACKUP);
    }
    
    /**
     * 递归添加目录到ZIP
     */
    private void addDirectoryToZip(Path sourceDir, Path baseDir, ZipOutputStream zos) throws IOException {
        Files.walk(sourceDir)
            .filter(Files::isRegularFile)
            .forEach(path -> {
                try {
                    String entryName = baseDir.relativize(path).toString().replace("\\", "/");
                    ZipEntry entry = new ZipEntry(entryName);
                    zos.putNextEntry(entry);
                    Files.copy(path, zos);
                    zos.closeEntry();
                } catch (IOException e) {
                    log.error("添加文件到ZIP失败: {}", path, e);
                }
            });
    }
    
    /**
     * 递归删除目录
     */
    private void deleteDirectory(Path directory) {
        try {
            if (Files.exists(directory)) {
                Files.walk(directory)
                    .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            log.warn("删除文件失败: {}", path, e);
                        }
                    });
            }
        } catch (IOException e) {
            log.warn("删除临时目录失败: {}", directory, e);
        }
    }
    
    /**
     * 从数据库URL中提取数据库名
     */
    private String extractDatabaseName(String url) {
        // ***********************************************?...
        String[] parts = url.split("/");
        String dbPart = parts[parts.length - 1];
        return dbPart.split("\\?")[0];
    }
    
    /**
     * 从数据库URL中提取主机名
     */
    private String extractHost(String url) {
        // ***********************************************?...
        String hostPart = url.split("//")[1].split("/")[0];
        return hostPart.split(":")[0];
    }
    
    /**
     * 从数据库URL中提取端口号
     */
    private String extractPort(String url) {
        // ***********************************************?...
        String hostPart = url.split("//")[1].split("/")[0];
        String[] hostPortParts = hostPart.split(":");
        return hostPortParts.length > 1 ? hostPortParts[1] : "3306";
    }
}
