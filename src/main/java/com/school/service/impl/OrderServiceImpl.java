package com.school.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.school.entity.Order;
import com.school.entity.Users;
import com.school.entity.SkillServiceDTO;
import com.school.mapper.OrderMapper;
import com.school.service.OrderService;
import com.school.service.SkillServiceService;
import com.school.service.UserService;
import com.school.service.WalletService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * 订单服务实现类
 */
@Slf4j
@Service
public class OrderServiceImpl implements OrderService {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private SkillServiceService skillServiceService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private WalletService walletService;
    
    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        Random random = new Random();
        int randomNum = random.nextInt(9999) + 1000; // 4位随机数
        return "ORD" + timestamp + randomNum;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Order createOrder(Integer userId, Long skillId, Integer quantity, 
                           Date appointmentTime, String appointmentAddress, 
                           Integer appointmentDuration, String appointmentRemark) {
        
        // 1. 验证参数
        if (quantity <= 0) {
            throw new RuntimeException("预约数量必须大于0");
        }
        
        if (appointmentTime.before(new Date())) {
            throw new RuntimeException("预约时间不能早于当前时间");
        }
        
        // 2. 检查是否重复预约
        if (!canMakeAppointment(userId, skillId, appointmentTime)) {
            throw new RuntimeException("该时间段已有预约，请选择其他时间");
        }
        
        // 3. 获取技能服务信息
        SkillServiceDTO skillServiceDTO = skillServiceService.getSkillServiceDetail(skillId);
        if (skillServiceDTO == null) {
            throw new RuntimeException("技能服务不存在");
        }
        if (skillServiceDTO.getStatus() != 2) {
            throw new RuntimeException("技能服务未上架");
        }

        // 4. 检查是否自己预约自己的服务
        if (skillServiceDTO.getUserId().equals(userId)) {
            throw new RuntimeException("不能预约自己发布的服务");
        }
        
        // 5. 获取用户信息
        Users consumer = userService.getById(userId);
        Users provider = userService.getById(skillServiceDTO.getUserId());

        if (consumer == null || provider == null) {
            throw new RuntimeException("用户信息不存在");
        }

        // 6. 计算总金额
        BigDecimal totalAmount = skillServiceDTO.getPrice().multiply(new BigDecimal(quantity));
        
        // 7. 检查余额是否充足
        if (!walletService.checkBalance(userId, totalAmount)) {
            throw new RuntimeException("余额不足，请先充值");
        }
        
        // 8. 创建订单
        Order order = new Order();
        order.setOrderNo(generateOrderNo());
        order.setBuyerId(userId);
        order.setSellerId(skillServiceDTO.getUserId());
        order.setServiceId(skillId);
        order.setTitle(skillServiceDTO.getTitle());
        order.setPrice(skillServiceDTO.getPrice());
        order.setStudentCount(quantity);
        order.setTotalAmount(totalAmount);
        order.setStatus(Order.OrderStatus.PENDING_PAYMENT.getCode());
        order.setAppointmentTime(appointmentTime);
        order.setLocation(appointmentAddress);
        order.setBuyerNote(appointmentRemark);
        order.setCreatedTime(new Date());
        order.setUpdatedTime(new Date());
        
        // 9. 保存订单
        orderMapper.insert(order);
        
        log.info("订单创建成功，订单号：{}，用户：{}，技能：{}，金额：{}", 
                order.getOrderNo(), userId, skillId, totalAmount);
        
        return order;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean payOrder(Long orderId, Integer userId) {
        // 1. 获取订单信息
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        
        // 2. 检查权限
        if (!order.getBuyerId().equals(userId)) {
            throw new RuntimeException("无权限操作该订单");
        }
        
        // 3. 检查订单状态
        if (order.getStatus() != Order.OrderStatus.PENDING_PAYMENT.getCode()) {
            throw new RuntimeException("订单状态不正确");
        }
        
        // 4. 冻结余额（相当于支付）
        boolean freezeSuccess = walletService.freezeBalance(
                userId, order.getTotalAmount(), orderId, "订单支付：" + order.getTitle());
        
        if (!freezeSuccess) {
            throw new RuntimeException("支付失败，余额不足");
        }
        
        // 5. 更新订单状态
        int updateResult = orderMapper.payOrder(
                orderId, Order.OrderStatus.PENDING_ACCEPT.getCode(), new Date(), 0);

        if (updateResult == 0) {
            // 支付失败，解冻余额
            walletService.unfreezeBalance(userId, order.getTotalAmount(), orderId, "订单支付失败解冻");
            throw new RuntimeException("订单支付失败，请重试");
        }
        
        log.info("订单支付成功，订单号：{}，用户：{}，金额：{}", 
                order.getOrderNo(), userId, order.getTotalAmount());
        
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean acceptOrder(Long orderId, Integer userId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        
        if (!order.getSellerId().equals(userId)) {
            throw new RuntimeException("无权限操作该订单");
        }

        if (order.getStatus() != Order.OrderStatus.PENDING_ACCEPT.getCode()) {
            throw new RuntimeException("订单状态不正确");
        }

        int updateResult = orderMapper.acceptOrder(
                orderId, Order.OrderStatus.IN_PROGRESS.getCode(), new Date(), 0);

        if (updateResult == 0) {
            throw new RuntimeException("接单失败，请重试");
        }

        log.info("订单接单成功，订单号：{}，服务提供者：{}", order.getOrderNo(), userId);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rejectOrder(Long orderId, Integer userId, String rejectReason) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        if (!order.getSellerId().equals(userId)) {
            throw new RuntimeException("无权限操作该订单");
        }

        if (order.getStatus() != Order.OrderStatus.PENDING_ACCEPT.getCode()) {
            throw new RuntimeException("订单状态不正确");
        }

        // 拒绝订单，解冻余额并退款
        boolean unfreezeSuccess = walletService.unfreezeBalance(
                order.getBuyerId(), order.getTotalAmount(), orderId, "订单被拒绝退款");

        if (!unfreezeSuccess) {
            throw new RuntimeException("退款失败");
        }

        int updateResult = orderMapper.rejectOrder(
                orderId, Order.OrderStatus.REJECTED.getCode(), rejectReason, 0);

        if (updateResult == 0) {
            throw new RuntimeException("拒绝订单失败，请重试");
        }

        log.info("订单拒绝成功，订单号：{}，服务提供者：{}，原因：{}",
                order.getOrderNo(), userId, rejectReason);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeOrder(Long orderId, Integer userId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        if (!order.getSellerId().equals(userId)) {
            throw new RuntimeException("无权限操作该订单");
        }

        if (order.getStatus() != Order.OrderStatus.IN_PROGRESS.getCode()) {
            throw new RuntimeException("订单状态不正确");
        }

        // 完成订单，扣除冻结金额并转账给服务提供者
        boolean deductSuccess = walletService.deductFrozenAmount(
                order.getBuyerId(), order.getTotalAmount(), orderId, "订单完成扣费");

        if (!deductSuccess) {
            throw new RuntimeException("扣费失败");
        }

        // 转账给服务提供者
        boolean transferSuccess = walletService.transferToProvider(
                order.getBuyerId(), order.getSellerId(), order.getTotalAmount(),
                orderId, "服务费收入：" + order.getTitle());

        if (!transferSuccess) {
            throw new RuntimeException("转账失败");
        }

        int updateResult = orderMapper.completeOrder(
                orderId, Order.OrderStatus.PENDING_CONFIRM.getCode(), new Date(), 0);

        if (updateResult == 0) {
            throw new RuntimeException("完成订单失败，请重试");
        }

        log.info("订单完成成功，订单号：{}，服务提供者：{}，金额：{}",
                order.getOrderNo(), userId, order.getTotalAmount());
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(Long orderId, Integer userId, String cancelReason) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        
        if (!order.getBuyerId().equals(userId)) {
            throw new RuntimeException("无权限操作该订单");
        }

        if (order.getStatus() != Order.OrderStatus.PENDING_PAYMENT.getCode() &&
            order.getStatus() != Order.OrderStatus.PENDING_ACCEPT.getCode()) {
            throw new RuntimeException("订单状态不正确，无法取消");
        }

        // 如果已支付，需要解冻余额
        if (order.getStatus() == Order.OrderStatus.PENDING_ACCEPT.getCode()) {
            boolean unfreezeSuccess = walletService.unfreezeBalance(
                    userId, order.getTotalAmount(), orderId, "订单取消退款");

            if (!unfreezeSuccess) {
                throw new RuntimeException("退款失败");
            }
        }

        int updateResult = orderMapper.cancelOrder(
                orderId, Order.OrderStatus.CANCELLED.getCode(), new Date(), cancelReason, 0);
        
        if (updateResult == 0) {
            throw new RuntimeException("取消订单失败，请重试");
        }
        
        log.info("订单取消成功，订单号：{}，用户：{}，原因：{}", 
                order.getOrderNo(), userId, cancelReason);
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmOrder(Long orderId, Integer userId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        if (!order.getBuyerId().equals(userId)) {
            throw new RuntimeException("无权限操作该订单");
        }

        if (order.getStatus() != Order.OrderStatus.PENDING_CONFIRM.getCode()) {
            throw new RuntimeException("订单状态不正确");
        }

        // 确认完成，订单状态变为已完成
        int updateResult = orderMapper.updateOrderStatus(
                orderId, Order.OrderStatus.COMPLETED.getCode(), 0);

        if (updateResult == 0) {
            throw new RuntimeException("确认失败，请重试");
        }

        log.info("订单确认完成，订单号：{}，用户：{}", order.getOrderNo(), userId);
        return true;
    }

    @Override
    public boolean rateOrder(Long orderId, Integer userId, Integer rating, String review) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        if (!order.getBuyerId().equals(userId)) {
            throw new RuntimeException("无权限操作该订单");
        }

        if (order.getStatus() != Order.OrderStatus.PENDING_CONFIRM.getCode() &&
            order.getStatus() != Order.OrderStatus.COMPLETED.getCode()) {
            throw new RuntimeException("订单状态不正确");
        }

        if (rating < 1 || rating > 5) {
            throw new RuntimeException("评分必须在1-5之间");
        }

        int updateResult = orderMapper.rateOrder(
                orderId, rating, review, new Date(), 0);

        if (updateResult == 0) {
            throw new RuntimeException("评价失败，请重试");
        }

        log.info("订单评价成功，订单号：{}，用户：{}，评分：{}",
                order.getOrderNo(), userId, rating);
        return true;
    }
    
    @Override
    public Order getOrderDetail(Long orderId, Integer userId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        
        // 检查权限：只有订单的消费者或服务提供者可以查看
        if (!order.getBuyerId().equals(userId) && !order.getSellerId().equals(userId)) {
            throw new RuntimeException("无权限查看该订单");
        }
        
        return order;
    }
    
    @Override
    public IPage<Order> getUserOrders(Integer userId, String type, Integer page, Integer size,
                                     Integer status, String keyword, Date startTime, Date endTime) {
        Page<Order> pageParam = new Page<>(page, size);
        return orderMapper.selectUserOrders(pageParam, userId, type, status, keyword, startTime, endTime);
    }
    
    @Override
    public boolean checkOrderPermission(Long orderId, Integer userId, String operation) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return false;
        }
        
        switch (operation) {
            case "pay":
            case "cancel":
            case "rate":
                return order.getBuyerId().equals(userId);
            case "accept":
            case "reject":
            case "complete":
                return order.getSellerId().equals(userId);
            case "view":
                return order.getBuyerId().equals(userId) || order.getSellerId().equals(userId);
            default:
                return false;
        }
    }
    
    @Override
    public boolean canMakeAppointment(Integer userId, Long skillId, Date appointmentTime) {
        Integer count = orderMapper.checkDuplicateAppointment(userId, skillId, appointmentTime);
        return count == 0;
    }

    @Override
    public BigDecimal getTotalOrderAmount() {
        return orderMapper.getTotalOrderAmount();
    }
}
