2025-06-05 09:07:23 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=9h17m55s853ms836µs500ns).
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"4mjhIdMJTowkUQpJX8OUm", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"lNET989BQFIPiZ9eNZRVm", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"lNET989BQFIPiZ9eNZRVm","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:07:47.343691300(LocalDateTime), 173(Long)
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"4mjhIdMJTowkUQpJX8OUm","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:07:47.343691300(LocalDateTime), 179(Long)
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"4mjhIdMJTowkUQpJX8OUm", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"lNET989BQFIPiZ9eNZRVm", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [POST /admin/backup/create, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"0", pragma:"no-cache", cache-control:"no-cache", x-request-id:"MC7kRK5MD4m1-0Hw1mxPu", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - POST "/admin/backup/create", parameters={}
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.admin.AdminController#createSystemBackup()
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.admin.AdminController.createSystemBackup
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:08:09 [http-nio-8081-exec-5] INFO  c.s.s.impl.SystemBackupServiceImpl - 开始创建系统备份...
2025-06-05 09:08:16 [http-nio-8081-exec-5] INFO  c.s.s.impl.SystemBackupServiceImpl - 数据库导出完成: /files/backup/e9ac2a4a4a3347619ac4ff1710c7799020250605090816.sql
2025-06-05 09:08:31 [http-nio-8081-exec-5] INFO  c.s.s.impl.SystemBackupServiceImpl - 文件打包完成: /files/backup/f3e52c84564a4ccb957c0547fb38149a20250605090831.gz
2025-06-05 09:08:36 [http-nio-8081-exec-5] INFO  c.s.s.impl.SystemBackupServiceImpl - 系统备份创建完成: /files/backup/fcec6d8a0d3740f78f4801d7e1e8cc7220250605090836.zip
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.admin.AdminController.createSystemBackup, 返回结果: {"code":200,"msg":"系统备份创建成功","data":"/files/backup/fcec6d8a0d3740f78f4801d7e1e8cc7220250605090836.zip"}
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 系统管理(String), 备份(String), 创建系统备份(String), /admin/backup/create(String), POST(String), [](String), {"x-request-id":"MC7kRK5MD4m1-0Hw1mxPu","sec-fetch-mode":"cors","content-length":"0","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 系统备份创建成功(String), "/files/backup/fcec6d8a0d3740f78f4801d7e1e8cc7220250605090836.zip"(String), 2(Long), 管理员(String), 2025-06-05T09:08:36.428568800(LocalDateTime), 27095(Long)
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "系统备份创建成功", "data": "/files/backup/fcec6d8a0d3740f78f4801d7e1e8cc72202506050908 (truncated)...]
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: POST /admin/backup/create, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"0", pragma:"no-cache", cache-control:"no-cache", x-request-id:"MC7kRK5MD4m1-0Hw1mxPu", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"2UjCFxLyXmE0ihzu1FCmt", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"2UjCFxLyXmE0ihzu1FCmt","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:08:57.476357900(LocalDateTime), 14(Long)
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"2UjCFxLyXmE0ihzu1FCmt", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"sSOxW-GsfWxT7QfzIQAvx", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"sSOxW-GsfWxT7QfzIQAvx","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:08:57.535213600(LocalDateTime), 18(Long)
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"sSOxW-GsfWxT7QfzIQAvx", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs/operation-types, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"HIR2z6Kr-bUnCb2haiSUd", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs/operation-types", parameters={}
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getOperationTypes()
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs?page=1&pageSize=25&startTime=2025-06-04%2016%3A00%3A00&endTime=2025-06-05%2015%3A59%3A59&userId=&username=&operationType=&operationModule=&responseCode=, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"8fZvXzMKoHcKWCDK7vR5-", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs?page=1&pageSize=25&startTime=2025-06-04%2016%3A00%3A00&endTime=2025-06-05%2015%3A59%3A59&userId=&username=&operationType=&operationModule=&responseCode=", parameters={masked}
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getSystemLogs(Integer, Integer, String, String, String, String, String, String, String)
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log WHERE (operation_time BETWEEN ? AND ?) ORDER BY operation_time DESC
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 2025-06-04T16:00(LocalDateTime), 2025-06-05T15:59:59(LocalDateTime)
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 697
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": com.baomidou.mybatisplus.extension.plugins.pagination.Page@5bc454 (truncated)...]
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs?page=1&pageSize=25&startTime=2025-06-04%2016%3A00%3A00&endTime=2025-06-05%2015%3A59%3A59&userId=&username=&operationType=&operationModule=&responseCode=, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"8fZvXzMKoHcKWCDK7vR5-", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 2490
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": [注册, 发送验证码, 验证码登录, 获取用户信息, 登出, 用户数据更新, 身份验证, 登录, 查询, 获取角色类型, 用户列表 (truncated)...]
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs/operation-types, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"HIR2z6Kr-bUnCb2haiSUd", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs/operation-modules, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"lbj6N5qYaBg34nSKV3NOA", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs/operation-modules", parameters={}
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getOperationModules()
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 
2025-06-05 09:08:59 [http-nio-8081-exec-8] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 2490
2025-06-05 09:08:59 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:08:59 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": [用户管理, 角色管理, 积分, 社交, 钱包管理, 管理, 技能管理, 订单管理, 社交管理, 信用评级管理, 系统管理]}]
2025-06-05 09:08:59 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:08:59 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs/operation-modules, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"lbj6N5qYaBg34nSKV3NOA", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs/response-codes, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"zj41z0J70kdgCpwEZt_qT", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs/response-codes", parameters={}
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getResponseCodes()
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 2490
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": [200, 500]}]
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs/response-codes, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"zj41z0J70kdgCpwEZt_qT", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"e3Ux_hLBVSyQWdTeKsw3S", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"4NNP72DMV7QjW_RvZSzfp", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"e3Ux_hLBVSyQWdTeKsw3S","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:10:22.625047800(LocalDateTime), 13(Long)
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"4NNP72DMV7QjW_RvZSzfp","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:10:22.637529300(LocalDateTime), 14(Long)
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"e3Ux_hLBVSyQWdTeKsw3S", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"4NNP72DMV7QjW_RvZSzfp", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:05 [main] INFO  com.school.Main - Starting Main using Java 21.0.4 with PID 67044 (E:\AllCode\project\schoolskill\target\classes started by Dong in E:\AllCode\project\schoolskill)
2025-06-05 09:19:05 [main] DEBUG com.school.Main - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 09:19:05 [main] INFO  com.school.Main - The following 1 profile is active: "dev"
2025-06-05 09:19:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:19:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 09:19:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.
2025-06-05 09:19:07 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-06-05 09:19:07 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 09:19:07 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 09:19:07 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 09:19:07 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1613 ms
2025-06-05 09:19:07 [main] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Filter 'requestLoggingFilter' configured for use
2025-06-05 09:19:07 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.school.entity.Permissions".
2025-06-05 09:19:07 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.school.entity.Permissions ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-05 09:19:07 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.school.mapper.RolesDao.getall] is ignored, because it exists, maybe from xml file
2025-06-05 09:19:07 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.school.mapper.SkillServiceMapper.incrementViewCount] is ignored, because it exists, maybe from xml file
2025-06-05 09:19:08 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 96 mappings in 'requestMappingHandlerMapping'
2025-06-05 09:19:08 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /files/**] in 'resourceHandlerMapping'
2025-06-05 09:19:08 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 09:19:08 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 09:19:09 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-06-05 09:19:09 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-05 09:19:09 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-06-05 09:19:11 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-05 09:19:11 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-05 09:19:15 [main] INFO  com.school.Main - Starting Main using Java 21.0.4 with PID 55776 (E:\AllCode\project\schoolskill\target\classes started by Dong in E:\AllCode\project\schoolskill)
2025-06-05 09:19:15 [main] DEBUG com.school.Main - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 09:19:15 [main] INFO  com.school.Main - The following 1 profile is active: "dev"
2025-06-05 09:19:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:19:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 09:19:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-06-05 09:19:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-06-05 09:19:16 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 09:19:16 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 09:19:17 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 09:19:17 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1772 ms
2025-06-05 09:19:17 [main] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Filter 'requestLoggingFilter' configured for use
2025-06-05 09:19:17 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.school.entity.Permissions".
2025-06-05 09:19:17 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.school.entity.Permissions ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-05 09:19:17 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.school.mapper.RolesDao.getall] is ignored, because it exists, maybe from xml file
2025-06-05 09:19:17 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.school.mapper.SkillServiceMapper.incrementViewCount] is ignored, because it exists, maybe from xml file
2025-06-05 09:19:18 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 96 mappings in 'requestMappingHandlerMapping'
2025-06-05 09:19:18 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /files/**] in 'resourceHandlerMapping'
2025-06-05 09:19:18 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 09:19:18 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 09:19:19 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8081 (http) with context path ''
2025-06-05 09:19:19 [main] INFO  com.school.Main - Started Main in 4.383 seconds (process running for 5.039)
2025-06-05 09:19:19 [http-nio-8081-exec-7] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 09:19:19 [http-nio-8081-exec-7] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@23993ab2
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@150ed9f1
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-05 09:19:19 [http-nio-8081-exec-7] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"OJiGVZwceryCm9PT_DBwp", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:19:19 [http-nio-8081-exec-7] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-05 09:19:20 [http-nio-8081-exec-7] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3209b123
2025-06-05 09:19:20 [http-nio-8081-exec-7] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"OJiGVZwceryCm9PT_DBwp","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:19:20.291128700(LocalDateTime), 525(Long)
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"OJiGVZwceryCm9PT_DBwp", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"TfTnmRM3THXQOdsy6B0Nh", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"TfTnmRM3THXQOdsy6B0Nh","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:19:20.418578300(LocalDateTime), 22(Long)
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"TfTnmRM3THXQOdsy6B0Nh", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs/operation-types, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"5oGb5SvKyddwTenVHy8L5", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs/operation-types", parameters={}
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getOperationTypes()
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs?page=1&pageSize=25&startTime=2025-06-04%2016%3A00%3A00&endTime=2025-06-05%2015%3A59%3A59&userId=&username=&operationType=&operationModule=&responseCode=, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"BJnvymAaPSNviZlLCPylv", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs?page=1&pageSize=25&startTime=2025-06-04%2016%3A00%3A00&endTime=2025-06-05%2015%3A59%3A59&userId=&username=&operationType=&operationModule=&responseCode=", parameters={masked}
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getSystemLogs(Integer, Integer, String, String, String, String, String, String, String)
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log WHERE (operation_time BETWEEN ? AND ?) ORDER BY operation_time DESC
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 2025-06-04T16:00(LocalDateTime), 2025-06-05T15:59:59(LocalDateTime)
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 701
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": com.baomidou.mybatisplus.extension.plugins.pagination.Page@31e65b (truncated)...]
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs?page=1&pageSize=25&startTime=2025-06-04%2016%3A00%3A00&endTime=2025-06-05%2015%3A59%3A59&userId=&username=&operationType=&operationModule=&responseCode=, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"BJnvymAaPSNviZlLCPylv", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 2494
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": [注册, 发送验证码, 验证码登录, 获取用户信息, 登出, 用户数据更新, 身份验证, 登录, 查询, 获取角色类型, 用户列表 (truncated)...]
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs/operation-types, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"5oGb5SvKyddwTenVHy8L5", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs/operation-modules, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"TZlmI3AT5GKMUks6_W-lH", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs/operation-modules", parameters={}
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getOperationModules()
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 2494
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": [用户管理, 角色管理, 积分, 社交, 钱包管理, 管理, 技能管理, 订单管理, 社交管理, 信用评级管理, 系统管理]}]
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs/operation-modules, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"TZlmI3AT5GKMUks6_W-lH", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [POST /admin/backup/create, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"0", pragma:"no-cache", cache-control:"no-cache", x-request-id:"kyu70U9U5TQ6fpmkkwTNp", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - POST "/admin/backup/create", parameters={}
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.admin.AdminController#createSystemBackup()
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.admin.AdminController.createSystemBackup
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:19:30 [http-nio-8081-exec-2] INFO  c.s.s.impl.SystemBackupServiceImpl - 开始创建系统备份...
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs/response-codes, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"bSKx0RAPEYABIhL6N_VYn", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs/response-codes", parameters={}
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getResponseCodes()
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 2494
2025-06-05 09:19:31 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:19:31 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": [200, 500]}]
2025-06-05 09:19:31 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:19:31 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs/response-codes, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"bSKx0RAPEYABIhL6N_VYn", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:35 [http-nio-8081-exec-2] INFO  c.s.s.impl.SystemBackupServiceImpl - 数据库导出完成: /files/backup/20250605_091935.sql
2025-06-05 09:20:00 [http-nio-8081-exec-2] INFO  c.s.s.impl.SystemBackupServiceImpl - 文件打包完成: /files/backup/20250605_092000.gz
2025-06-05 09:20:13 [http-nio-8081-exec-2] INFO  c.s.s.impl.SystemBackupServiceImpl - 系统备份创建完成: /files/backup/20250605_092013.zip
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.admin.AdminController.createSystemBackup, 返回结果: {"code":200,"msg":"系统备份创建成功","data":"/files/backup/20250605_092013.zip"}
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 系统管理(String), 备份(String), 创建系统备份(String), /admin/backup/create(String), POST(String), [](String), {"x-request-id":"kyu70U9U5TQ6fpmkkwTNp","sec-fetch-mode":"cors","content-length":"0","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 系统备份创建成功(String), "/files/backup/20250605_092013.zip"(String), 2(Long), 管理员(String), 2025-06-05T09:20:13.420237(LocalDateTime), 43015(Long)
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "系统备份创建成功", "data": "/files/backup/20250605_092013.zip"}]
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: POST /admin/backup/create, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"0", pragma:"no-cache", cache-control:"no-cache", x-request-id:"kyu70U9U5TQ6fpmkkwTNp", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:20:19 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/backup/20250605_092013.zip, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", sec-ch-ua-platform:""Windows"", upgrade-insecure-requests:"1", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", sec-fetch-site:"cross-site", sec-fetch-mode:"navigate", sec-fetch-user:"?1", sec-fetch-dest:"document", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", cookie:"agh_session=d8386b3b9423d0e75506e51d30b3a7e7; csrftoken=2GlyhZ7qrmQc2PXyRnHMqpSNailbIfBp; accessToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE2NDY4Mzg2MjUsInJuU3RyIjoiNURHUVVCZGxSbG5KZUNUb3BSdEZmeE5ZSnZSWG14elEiLCJuYW1lIjoi566h55CGIn0.e0WYATw8Bq3mZywnPhB22nrZvRJls2OczMndumyeFBk"]]
2025-06-05 09:20:19 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/backup/20250605_092013.zip", parameters={}
2025-06-05 09:20:19 [http-nio-8081-exec-9] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:20:22 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:20:22 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/backup/20250605_092013.zip, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", sec-ch-ua-platform:""Windows"", upgrade-insecure-requests:"1", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", sec-fetch-site:"cross-site", sec-fetch-mode:"navigate", sec-fetch-user:"?1", sec-fetch-dest:"document", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", cookie:"agh_session=d8386b3b9423d0e75506e51d30b3a7e7; csrftoken=2GlyhZ7qrmQc2PXyRnHMqpSNailbIfBp; accessToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE2NDY4Mzg2MjUsInJuU3RyIjoiNURHUVVCZGxSbG5KZUNUb3BSdEZmeE5ZSnZSWG14elEiLCJuYW1lIjoi566h55CGIn0.e0WYATw8Bq3mZywnPhB22nrZvRJls2OczMndumyeFBk"]]
2025-06-05 09:24:33 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-05 09:24:33 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-05 09:24:38 [main] INFO  com.school.Main - Starting Main using Java 21.0.4 with PID 10824 (E:\AllCode\project\schoolskill\target\classes started by Dong in E:\AllCode\project\schoolskill)
2025-06-05 09:24:38 [main] DEBUG com.school.Main - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 09:24:38 [main] INFO  com.school.Main - The following 1 profile is active: "dev"
2025-06-05 09:24:39 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:24:39 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 09:24:39 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2025-06-05 09:24:40 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-06-05 09:24:40 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 09:24:40 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 09:24:40 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 09:24:40 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1899 ms
2025-06-05 09:24:40 [main] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Filter 'requestLoggingFilter' configured for use
2025-06-05 09:24:41 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.school.entity.Permissions".
2025-06-05 09:24:41 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.school.entity.Permissions ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-05 09:24:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.school.mapper.RolesDao.getall] is ignored, because it exists, maybe from xml file
2025-06-05 09:24:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.school.mapper.SkillServiceMapper.incrementViewCount] is ignored, because it exists, maybe from xml file
2025-06-05 09:24:42 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 96 mappings in 'requestMappingHandlerMapping'
2025-06-05 09:24:42 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /files/**] in 'resourceHandlerMapping'
2025-06-05 09:24:42 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 09:24:42 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 09:24:42 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8081 (http) with context path ''
2025-06-05 09:24:42 [main] INFO  com.school.Main - Started Main in 4.378 seconds (process running for 5.043)
2025-06-05 09:24:48 [http-nio-8081-exec-4] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 09:24:48 [http-nio-8081-exec-4] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 09:24:48 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-06-05 09:24:48 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-06-05 09:24:48 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-06-05 09:24:48 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@290c4de
2025-06-05 09:24:48 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@4a54b3e0
2025-06-05 09:24:48 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-05 09:24:48 [http-nio-8081-exec-4] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 09:24:48 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /social/posts/hot?limit=5, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"PebCD8GI5XCVK0q4uusPj", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:24:48 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /social/users/recommended?limit=5, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"GaWV_k0Z7SmEPkgp5XuY5", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:24:48 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"01mhiRRPhEnCNeqv2mn66", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:24:48 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /posts/list?page=1&pageSize=10&followed=false, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"HWUzDu87UF8Vvk95MvRTF", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:24:48 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/social/users/recommended?limit=5", parameters={masked}
2025-06-05 09:24:48 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:24:48 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - GET "/social/posts/hot?limit=5", parameters={masked}
2025-06-05 09:24:48 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - GET "/posts/list?page=1&pageSize=10&followed=false", parameters={masked}
2025-06-05 09:24:48 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.social.SocialController#getRecommendedUsers(Integer)
2025-06-05 09:24:48 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.social.PostController#getPostList(Integer, Integer, Integer, String, Boolean)
2025-06-05 09:24:48 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.social.SocialController#getHotPosts(Integer)
2025-06-05 09:24:48 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:24:48 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.social.SocialController.getRecommendedUsers
2025-06-05 09:24:48 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:24:48 [http-nio-8081-exec-8] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.social.SocialController.getHotPosts
2025-06-05 09:24:48 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.social.PostController.getPostList
2025-06-05 09:24:48 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:24:48 [http-nio-8081-exec-8] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [5]
2025-06-05 09:24:48 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [5]
2025-06-05 09:24:48 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [1,10,null,null,false]
2025-06-05 09:24:48 [http-nio-8081-exec-6] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-05 09:24:49 [http-nio-8081-exec-6] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2a48438b
2025-06-05 09:24:49 [http-nio-8081-exec-6] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG c.s.m.U.selectRecommendedUsers - ==>  Preparing: SELECT u.user_id as userId, u.name as userName, u.avatar as userAvatar, u.user_type as userType, COALESCE(follower_counts.followers_count, 0) as followersCount, CASE WHEN uf.id IS NOT NULL THEN 1 ELSE 0 END as isFollowed FROM users u LEFT JOIN ( SELECT following_id, COUNT(*) as followers_count FROM user_follows GROUP BY following_id ) follower_counts ON u.user_id = follower_counts.following_id LEFT JOIN user_follows uf ON uf.follower_id = ? AND uf.following_id = u.user_id WHERE u.user_id != ? AND u.status = 1 ORDER BY follower_counts.followers_count DESC, u.user_id ASC LIMIT ?
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG c.s.mapper.PostMapper.selectHotPosts - ==>  Preparing: SELECT p.post_id as id, p.user_id as userId, u.name as userName, u.avatar as userAvatar, u.user_type as userType, p.content, p.images, p.skill_id as skillId, ss.title as skillTitle, p.like_count as likeCount, p.comment_count as commentCount, p.view_count as viewCount, p.created_time as createdTime, CASE WHEN l.like_id IS NOT NULL THEN 1 ELSE 0 END as isLiked, CASE WHEN uf.id IS NOT NULL THEN 1 ELSE 0 END as isFollowed FROM posts p LEFT JOIN users u ON p.user_id = u.user_id LEFT JOIN skill_services ss ON p.skill_id = ss.service_id LEFT JOIN likes l ON l.target_id = p.post_id AND l.target_type = 'post' AND l.user_id = ? LEFT JOIN user_follows uf ON uf.following_id = p.user_id AND uf.follower_id = ? WHERE p.status = 1 ORDER BY p.like_count DESC, p.created_time DESC LIMIT ?
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG c.s.m.U.selectRecommendedUsers - ==> Parameters: 2(Integer), 2(Integer), 5(Integer)
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG c.s.mapper.PostMapper.selectHotPosts - ==> Parameters: 2(Integer), 2(Integer), 5(Integer)
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG c.s.m.U.selectRecommendedUsers - <==      Total: 2
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG c.s.mapper.PostMapper.selectHotPosts - <==      Total: 2
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.s.m.P.selectPostListWithUser - ==>  Preparing: SELECT p.post_id as id, p.user_id as userId, u.name as userName, u.avatar as userAvatar, p.content, p.images, p.skill_id as skillId, ss.title as skillTitle, p.like_count as likeCount, p.comment_count as commentCount, p.view_count as viewCount, p.created_time as createdTime, CASE WHEN l.like_id IS NOT NULL THEN 1 ELSE 0 END as isLiked, CASE WHEN uf.id IS NOT NULL THEN 1 ELSE 0 END as isFollowed FROM posts p LEFT JOIN users u ON p.user_id = u.user_id LEFT JOIN skill_services ss ON p.skill_id = ss.service_id LEFT JOIN likes l ON l.target_id = p.post_id AND l.target_type = 'post' AND l.user_id = ? LEFT JOIN user_follows uf ON uf.following_id = p.user_id AND uf.follower_id = ? WHERE p.status = 1 ORDER BY p.created_time DESC LIMIT ?, ?
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.s.m.P.selectPostListWithUser - ==> Parameters: 2(Integer), 2(Integer), 0(Integer), 10(Integer)
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.s.m.P.selectPostListWithUser - <==      Total: 2
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.s.m.PostMapper.selectPostCount - ==>  Preparing: SELECT COUNT(*) FROM posts p WHERE p.status = 1
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.s.m.PostMapper.selectPostCount - ==> Parameters: 
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.s.m.PostMapper.selectPostCount - <==      Total: 1
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.social.SocialController.getRecommendedUsers, 返回结果: {"code":200,"msg":"查询成功","data":[{"userType":1,"followersCount":1,"userName":"测试账户二","userId":1,"isFollowed":0},{"userType":1,"followersCount":1,"userName":"测试账户一","userId":3,"isFollowed":0}]}
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.social.SocialController.getHotPosts, 返回结果: {"code":200,"msg":"查询成功","data":[{"images":["/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg"],"isLiked":0,"createdTime":1749025083000,"likeCount":1,"id":2,"userType":1,"viewCount":31,"userName":"测试账户二","userId":1,"content":"# 权限架构是什么\n权限架构（Permission Architecture）是一种管理和控制系统中用户访问资源的方式。它定义了谁可以访问哪些资源以及如何进行这些访问。权限架构的主要目标是确保系统的安全性和数据的完整性，防止未授权的访问和潜在的安全威胁。\n通俗一点说，对于一个系统，我们需要规定每个用户能干什么，不能干什么，这也就是每个用户的权限，我们需要设置合理的权限去管理每一个接口。\n# 权限架构包含什么\n一个最基础的权限架构通常会包含三个内容，两种，一个是具体的用户，一个是用户组，一个是权限，另一种是，用户，角色，权限。在大部分的系统中，我们通常只会将权限赋予用户组，然后用户属于用户组。\n# 五表结构\n经典的五表结构是指在权限管理系统中常见的五张核心数据表，用于管理用户、角色、权限以及它们之间的关系。这种结构能够清晰地定义和维护权限体系，广泛应用于基于角色的访问控制（RBAC）模型。五表结构通常包括以下五张表：\n## 用户表（users）\n- 记录系统中所有用户的信息。\n- 典型字段：用户ID、用户名、密码、电子邮件、状态（如激活或禁用）等。\n```\nCREATE TABLE users (\n  user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',\n  username VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '用户名',\n  PASSWORD VARCHAR ( 255 ) NOT NULL COMMENT '密码',\n  email VARCHAR ( 255 ) NOT NULL COMMENT '邮箱，用于登录',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '用户表';\n```\n## 角色表（Role）\n- 存储角色的基本信息，如角色ID、角色名、角色描述等。角色是权限的集合，用户通过角色来获得权限。\n```\nCREATE TABLE roles (\n  role_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',\n  role_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '角色名称',\n  description VARCHAR ( 255 ) COMMENT '角色描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '角色表';\n```\n## 权限表（Permission）\n- 存储权限的基本信息，如权限ID、权限名、权限描述等。权限通常对应于系统中的某些操作或资源的访问控制。\n```\nCREATE TABLE permissions (\n  permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',\n  permission_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '权限名称',\n  description VARCHAR ( 255 ) COMMENT '权限描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '权限表';\n```\n## 用户角色关系表（UserRole)：\n- 存储用户和角色之间的关系，通常包括用户ID和角色ID。这个表用来表示一个用户拥有哪些角色。\n```\nCREATE TABLE user_roles (\n  user_id INT COMMENT '用户ID',\n  role_id INT COMMENT '角色ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( user_id, role_id ),\n  FOREIGN KEY ( user_id ) REFERENCES users ( user_id ) ON DELETE CASCADE ON UPDATE CASCADE,\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '用户角色关联表';\n```\n## 角色权限关联表（rolePermissions）\n- 存储权限与角色之间的关系，通常包括权限ID和角色ID。这个表用来表示一个角色拥有哪些权限\n```\nCREATE TABLE role_permissions (\n  role_id INT COMMENT '角色ID',\n  permission_id INT COMMENT '权限ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( role_id, permission_id ),\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE,\nFOREIGN KEY ( permission_id ) REFERENCES permissions ( permission_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '角色权限关联表';\n```\n","isFollowed":0,"commentCount":1},{"images":["/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg","/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg"],"isLiked":0,"createdTime":1749022456000,"likeCount":1,"id":1,"userType":1,"viewCount":26,"userName":"测试账户一","userId":3,"content":"·12412412","isFollowed":0,"commentCount":3}]}
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.social.PostController.getPostList, 返回结果: {"code":200,"msg":"动态列表查询成功","data":{"total":2,"current":1,"pages":1,"size":10,"records":[{"images":["/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg"],"isLiked":0,"createdTime":1749025083000,"likeCount":1,"id":2,"viewCount":31,"userName":"测试账户二","userId":1,"content":"# 权限架构是什么\n权限架构（Permission Architecture）是一种管理和控制系统中用户访问资源的方式。它定义了谁可以访问哪些资源以及如何进行这些访问。权限架构的主要目标是确保系统的安全性和数据的完整性，防止未授权的访问和潜在的安全威胁。\n通俗一点说，对于一个系统，我们需要规定每个用户能干什么，不能干什么，这也就是每个用户的权限，我们需要设置合理的权限去管理每一个接口。\n# 权限架构包含什么\n一个最基础的权限架构通常会包含三个内容，两种，一个是具体的用户，一个是用户组，一个是权限，另一种是，用户，角色，权限。在大部分的系统中，我们通常只会将权限赋予用户组，然后用户属于用户组。\n# 五表结构\n经典的五表结构是指在权限管理系统中常见的五张核心数据表，用于管理用户、角色、权限以及它们之间的关系。这种结构能够清晰地定义和维护权限体系，广泛应用于基于角色的访问控制（RBAC）模型。五表结构通常包括以下五张表：\n## 用户表（users）\n- 记录系统中所有用户的信息。\n- 典型字段：用户ID、用户名、密码、电子邮件、状态（如激活或禁用）等。\n```\nCREATE TABLE users (\n  user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',\n  username VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '用户名',\n  PASSWORD VARCHAR ( 255 ) NOT NULL COMMENT '密码',\n  email VARCHAR ( 255 ) NOT NULL COMMENT '邮箱，用于登录',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '用户表';\n```\n## 角色表（Role）\n- 存储角色的基本信息，如角色ID、角色名、角色描述等。角色是权限的集合，用户通过角色来获得权限。\n```\nCREATE TABLE roles (\n  role_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',\n  role_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '角色名称',\n  description VARCHAR ( 255 ) COMMENT '角色描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '角色表';\n```\n## 权限表（Permission）\n- 存储权限的基本信息，如权限ID、权限名、权限描述等。权限通常对应于系统中的某些操作或资源的访问控制。\n```\nCREATE TABLE permissions (\n  permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',\n  permission_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '权限名称',\n  description VARCHAR ( 255 ) COMMENT '权限描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '权限表';\n```\n## 用户角色关系表（UserRole)：\n- 存储用户和角色之间的关系，通常包括用户ID和角色ID。这个表用来表示一个用户拥有哪些角色。\n```\nCREATE TABLE user_roles (\n  user_id INT COMMENT '用户ID',\n  role_id INT COMMENT '角色ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( user_id, role_id ),\n  FOREIGN KEY ( user_id ) REFERENCES users ( user_id ) ON DELETE CASCADE ON UPDATE CASCADE,\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '用户角色关联表';\n```\n## 角色权限关联表（rolePermissions）\n- 存储权限与角色之间的关系，通常包括权限ID和角色ID。这个表用来表示一个角色拥有哪些权限\n```\nCREATE TABLE role_permissions (\n  role_id INT COMMENT '角色ID',\n  permission_id INT COMMENT '权限ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( role_id, permission_id ),\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE,\nFOREIGN KEY ( permission_id ) REFERENCES permissions ( permission_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '角色权限关联表';\n```\n","isFollowed":0,"commentCount":1},{"images":["/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg","/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg"],"isLiked":0,"createdTime":1749022456000,"likeCount":1,"id":1,"viewCount":26,"userName":"测试账户一","userId":3,"content":"·12412412","isFollowed":0,"commentCount":3}]}}
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 社交管理(String), 查看推荐用户(String), 获取推荐用户列表(String), /social/users/recommended(String), GET(String), [5](String), {"x-request-id":"GaWV_k0Z7SmEPkgp5XuY5","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 查询成功(String), [{"userType":1,"followersCount":1,"userName":"测试账户二","userId":1,"isFollowed":0},{"userType":1,"followersCount":1,"userName":"测试账户一","userId":3,"isFollowed":0}](String), 2(Long), 管理员(String), 2025-06-05T09:24:49.413485400(LocalDateTime), 631(Long)
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 社交管理(String), 查看热门帖子(String), 获取热门帖子列表(String), /social/posts/hot(String), GET(String), [5](String), {"x-request-id":"PebCD8GI5XCVK0q4uusPj","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 查询成功(String), [{"images":["/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg"],"isLiked":0,"createdTime":1749025083000,"likeCount":1,"id":2,"userType":1,"viewCount":31,"userName":"测试账户二","userId":1,"content":"# 权限架构是什么\n权限架构（Permission Architecture）是一种管理和控制系统中用户访问资源的方式。它定义了谁可以访问哪些资源以及如何进行这些访问。权限架构的主要目标是确保系统的安全性和数据的完整性，防止未授权的访问和潜在的安全威胁。\n通俗一点说，对于一个系统，我们需要规定每个用户能干什么，不能干什么，这也就是每个用户的权限，我们需要设置合理的权限去管理每一个接口。\n# 权限架构包含什么\n一个最基础的权限架构通常会包含三个内容，两种，一个是具体的用户，一个是用户组，一个是权限，另一种是，用户，角色，权限。在大部分的系统中，我们通常只会将权限赋予用户组，然后用户属于用户组。\n# 五表结构\n经典的五表结构是指在权限管理系统中常见的五张核心数据表，用于管理用户、角色、权限以及它们之间的关系。这种结构能够清晰地定义和维护权限体系，广泛应用于基于角色的访问控制（RBAC）模型。五表结构通常包括以下五张表：\n## 用户表（users）\n- 记录系统中所有用户的信息。\n- 典型字段：用户ID、用户名、密码、电子邮件、状态（如激活或禁用）等。\n```\nCREATE TABLE users (\n  user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',\n  username VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '用户名',\n  PASSWORD VARCHAR ( 255 ) NOT NULL COMMENT '密码',\n  email VARCHAR ( 255 ) NOT NULL COMMENT '邮箱，用于登录',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '用户表';\n```\n## 角色表（Role）\n- 存储角色的基本信息，如角色ID、角色名、角色描述等。角色是权限的集合，用户通过角色来获得权限。\n```\nCREATE TABLE roles (\n  role_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',\n  role_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '角色名称',\n  description VARCHAR ( 255 ) COMMENT '角色描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '角色表';\n```\n## 权限表（Permission）\n- 存储权限的基本信息，如权限ID、权限名、权限描述等。权限通常对应于系统中的某些操作或资源的访问控制。\n```\nCREATE TABLE permissions (\n  permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',\n  permission_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '权限名称',\n  description VARCHAR ( 255 ) COMMENT '权限描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '权限表';\n```\n## 用户角色关系表（UserRole)：\n- 存储用户和角色之间的关系，通常包括用户ID和角色ID。这个表用来表示一个用户拥有哪些角色。\n```\nCREATE TABLE user_roles (\n  user_id INT COMMENT '用户ID',\n  role_id INT COMMENT '角色ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( user_id, role_id ),\n  FOREIGN KEY ( user_id ) REFERENCES users ( user_id ) ON DELETE CASCADE ON UPDATE CASCADE,\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '用户角色关联表';\n```\n## 角色权限关联表（rolePermissions）\n- 存储权限与角色之间的关系，通常包括权限ID和角色ID。这个表用来表示一个角色拥有哪些权限\n```\nCREATE TABLE role_permissions (\n  role_id INT COMMENT '角色ID',\n  permission_id INT COMMENT '权限ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( role_id, permission_id ),\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE,\nFOREIGN KEY ( permission_id ) REFERENCES permissions ( permission_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '角色权限关联表';\n```\n","isFollowed":0,"commentCount":1},{"images":["/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg","/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg"],"isLiked":0,"createdTime":1749022456000,"likeCount":1,"id":1,"userType":1,"viewCount":26,"userName":"测试账户一","userId":3,"content":"·12412412","isFollowed":0,"commentCount":3}](String), 2(Long), 管理员(String), 2025-06-05T09:24:49.419359500(LocalDateTime), 637(Long)
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 社交(String), 查询(String), 查询动态列表(String), /posts/list(String), GET(String), [1,10,null,null,false](String), {"x-request-id":"HWUzDu87UF8Vvk95MvRTF","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 动态列表查询成功(String), {"total":2,"current":1,"pages":1,"size":10,"records":[{"images":["/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg"],"isLiked":0,"createdTime":1749025083000,"likeCount":1,"id":2,"viewCount":31,"userName":"测试账户二","userId":1,"content":"# 权限架构是什么\n权限架构（Permission Architecture）是一种管理和控制系统中用户访问资源的方式。它定义了谁可以访问哪些资源以及如何进行这些访问。权限架构的主要目标是确保系统的安全性和数据的完整性，防止未授权的访问和潜在的安全威胁。\n通俗一点说，对于一个系统，我们需要规定每个用户能干什么，不能干什么，这也就是每个用户的权限，我们需要设置合理的权限去管理每一个接口。\n# 权限架构包含什么\n一个最基础的权限架构通常会包含三个内容，两种，一个是具体的用户，一个是用户组，一个是权限，另一种是，用户，角色，权限。在大部分的系统中，我们通常只会将权限赋予用户组，然后用户属于用户组。\n# 五表结构\n经典的五表结构是指在权限管理系统中常见的五张核心数据表，用于管理用户、角色、权限以及它们之间的关系。这种结构能够清晰地定义和维护权限体系，广泛应用于基于角色的访问控制（RBAC）模型。五表结构通常包括以下五张表：\n## 用户表（users）\n- 记录系统中所有用户的信息。\n- 典型字段：用户ID、用户名、密码、电子邮件、状态（如激活或禁用）等。\n```\nCREATE TABLE users (\n  user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',\n  username VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '用户名',\n  PASSWORD VARCHAR ( 255 ) NOT NULL COMMENT '密码',\n  email VARCHAR ( 255 ) NOT NULL COMMENT '邮箱，用于登录',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '用户表';\n```\n## 角色表（Role）\n- 存储角色的基本信息，如角色ID、角色名、角色描述等。角色是权限的集合，用户通过角色来获得权限。\n```\nCREATE TABLE roles (\n  role_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',\n  role_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '角色名称',\n  description VARCHAR ( 255 ) COMMENT '角色描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '角色表';\n```\n## 权限表（Permission）\n- 存储权限的基本信息，如权限ID、权限名、权限描述等。权限通常对应于系统中的某些操作或资源的访问控制。\n```\nCREATE TABLE permissions (\n  permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',\n  permission_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '权限名称',\n  description VARCHAR ( 255 ) COMMENT '权限描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '权限表';\n```\n## 用户角色关系表（UserRole)：\n- 存储用户和角色之间的关系，通常包括用户ID和角色ID。这个表用来表示一个用户拥有哪些角色。\n```\nCREATE TABLE user_roles (\n  user_id INT COMMENT '用户ID',\n  role_id INT COMMENT '角色ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( user_id, role_id ),\n  FOREIGN KEY ( user_id ) REFERENCES users ( user_id ) ON DELETE CASCADE ON UPDATE CASCADE,\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '用户角色关联表';\n```\n## 角色权限关联表（rolePermissions）\n- 存储权限与角色之间的关系，通常包括权限ID和角色ID。这个表用来表示一个角色拥有哪些权限\n```\nCREATE TABLE role_permissions (\n  role_id INT COMMENT '角色ID',\n  permission_id INT COMMENT '权限ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( role_id, permission_id ),\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE,\nFOREIGN KEY ( permission_id ) REFERENCES permissions ( permission_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '角色权限关联表';\n```\n","isFollowed":0,"commentCount":1},{"images":["/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg","/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg"],"isLiked":0,"createdTime":1749022456000,"likeCount":1,"id":1,"viewCount":26,"userName":"测试账户一","userId":3,"content":"·12412412","isFollowed":0,"commentCount":3}]}(String), 2(Long), 管理员(String), 2025-06-05T09:24:49.419359500(LocalDateTime), 635(Long)
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"01mhiRRPhEnCNeqv2mn66","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:24:49.432888300(LocalDateTime), 651(Long)
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "查询成功", "data": [{userType=1, followersCount=1, userName=测试账户二, userId=1, isFol (truncated)...]
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "查询成功", "data": [{images=[/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d202 (truncated)...]
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "动态列表查询成功", "data": {total=2, current=1, pages=1, size=10, records=[{images=[/f (truncated)...]
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:24:49 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /posts/list?page=1&pageSize=10&followed=false, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"HWUzDu87UF8Vvk95MvRTF", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:24:49 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /social/posts/hot?limit=5, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"PebCD8GI5XCVK0q4uusPj", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:24:49 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /social/users/recommended?limit=5, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"GaWV_k0Z7SmEPkgp5XuY5", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:24:49 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"01mhiRRPhEnCNeqv2mn66", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:24:49 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:24:49 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg", parameters={}
2025-06-05 09:24:49 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:24:49 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:24:49 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg", parameters={}
2025-06-05 09:24:49 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg", parameters={}
2025-06-05 09:24:49 [http-nio-8081-exec-10] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:24:49 [http-nio-8081-exec-4] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:24:49 [http-nio-8081-exec-9] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:24:49 [http-nio-8081-exec-4] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-06-05 09:24:49 [http-nio-8081-exec-10] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-06-05 09:24:49 [http-nio-8081-exec-9] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-06-05 09:24:49 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.school.exception.GlobalExceptionHandler#handlerException(Exception)
2025-06-05 09:24:49 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.school.exception.GlobalExceptionHandler#handlerException(Exception)
2025-06-05 09:24:49 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.school.exception.GlobalExceptionHandler#handlerException(Exception)
2025-06-05 09:24:49 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json;q=0.8', given [image/avif, image/webp, image/apng, image/svg+xml, image/*, */*;q=0.8] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:24:49 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 500, "msg": "出现问题，请寻求管理员帮助", "data": null}]
2025-06-05 09:24:49 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json;q=0.8', given [image/avif, image/webp, image/apng, image/svg+xml, image/*, */*;q=0.8] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:24:49 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 500, "msg": "出现问题，请寻求管理员帮助", "data": null}]
2025-06-05 09:24:49 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg.]
2025-06-05 09:24:49 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:24:49 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json;q=0.8', given [image/avif, image/webp, image/apng, image/svg+xml, image/*, */*;q=0.8] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:24:49 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:24:49 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 500, "msg": "出现问题，请寻求管理员帮助", "data": null}]
2025-06-05 09:24:49 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg.]
2025-06-05 09:24:49 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:24:49 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:24:49 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg.]
2025-06-05 09:24:49 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:24:49 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:24:57 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [POST /upload, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"445656", pragma:"no-cache", cache-control:"no-cache", x-request-id:"8LpM6WHusMkXc6VLu5Rww", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", Content-Type:"multipart/form-data;boundary=----WebKitFormBoundaryopfloM9Kcw91VSHI;charset=UTF-8"]]
2025-06-05 09:24:57 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - POST "/upload", parameters={multipart}
2025-06-05 09:24:57 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [POST /upload, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"341988", pragma:"no-cache", cache-control:"no-cache", x-request-id:"ja8FZL92YKfetSFKTCiBi", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", Content-Type:"multipart/form-data;boundary=----WebKitFormBoundary47P2TN4G4bqEAqlk;charset=UTF-8"]]
2025-06-05 09:24:57 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - POST "/upload", parameters={multipart}
2025-06-05 09:24:57 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [POST /upload, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"222154", pragma:"no-cache", cache-control:"no-cache", x-request-id:"G5gNqKmA38pEVSrsrRZ6_", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", Content-Type:"multipart/form-data;boundary=----WebKitFormBoundarymBGh7Sz0DqMZZIO2;charset=UTF-8"]]
2025-06-05 09:24:57 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - POST "/upload", parameters={multipart}
2025-06-05 09:24:57 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [POST /upload, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"823855", pragma:"no-cache", cache-control:"no-cache", x-request-id:"upo63ZF4moQlYPrrlGuwe", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", Content-Type:"multipart/form-data;boundary=----WebKitFormBoundarywBxDKvkaOZFqAeUm;charset=UTF-8"]]
2025-06-05 09:24:57 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - POST "/upload", parameters={multipart}
2025-06-05 09:24:57 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.FileUploadController#uploadFile(MultipartFile, String)
2025-06-05 09:24:57 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.FileUploadController#uploadFile(MultipartFile, String)
2025-06-05 09:24:57 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.FileUploadController#uploadFile(MultipartFile, String)
2025-06-05 09:24:57 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.FileUploadController#uploadFile(MultipartFile, String)
2025-06-05 09:24:57 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:24:57 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:24:57 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": "/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457 (truncated)...]
2025-06-05 09:24:57 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:24:57 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": "/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457 (truncated)...]
2025-06-05 09:24:57 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:24:57 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": "/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457 (truncated)...]
2025-06-05 09:24:57 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": "/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457 (truncated)...]
2025-06-05 09:24:57 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:24:57 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:24:57 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:24:57 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:24:57 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: POST /upload, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"222154", pragma:"no-cache", cache-control:"no-cache", x-request-id:"G5gNqKmA38pEVSrsrRZ6_", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", Content-Type:"multipart/form-data;boundary=----WebKitFormBoundarymBGh7Sz0DqMZZIO2;charset=UTF-8"]]
2025-06-05 09:24:57 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: POST /upload, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"823855", pragma:"no-cache", cache-control:"no-cache", x-request-id:"upo63ZF4moQlYPrrlGuwe", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", Content-Type:"multipart/form-data;boundary=----WebKitFormBoundarywBxDKvkaOZFqAeUm;charset=UTF-8"]]
2025-06-05 09:24:57 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: POST /upload, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"341988", pragma:"no-cache", cache-control:"no-cache", x-request-id:"ja8FZL92YKfetSFKTCiBi", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", Content-Type:"multipart/form-data;boundary=----WebKitFormBoundary47P2TN4G4bqEAqlk;charset=UTF-8"]]
2025-06-05 09:24:57 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: POST /upload, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"445656", pragma:"no-cache", cache-control:"no-cache", x-request-id:"8LpM6WHusMkXc6VLu5Rww", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", Content-Type:"multipart/form-data;boundary=----WebKitFormBoundaryopfloM9Kcw91VSHI;charset=UTF-8"]]
2025-06-05 09:25:01 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [POST /posts/create, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"316", pragma:"no-cache", cache-control:"no-cache", x-request-id:"L8Ex0j6sLEScUEJELq2RU", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", Content-Type:"application/json;charset=UTF-8"]]
2025-06-05 09:25:01 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - POST "/posts/create", parameters={}
2025-06-05 09:25:01 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.social.PostController#createPost(Map)
2025-06-05 09:25:01 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{content=123124, images=[/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg, /file (truncated)...]
2025-06-05 09:25:01 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.social.PostController.createPost
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [{"content":"123124","images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"]}]
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG com.school.mapper.PostMapper.insert - ==>  Preparing: INSERT INTO posts ( user_id, content, images, like_count, comment_count, view_count, status, created_time, updated_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG com.school.mapper.PostMapper.insert - ==> Parameters: 2(Integer), 123124(String), ["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"](String), 0(Integer), 0(Integer), 0(Integer), 1(Integer), 2025-06-05 09:25:02.055(Timestamp), 2025-06-05 09:25:02.055(Timestamp)
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG com.school.mapper.PostMapper.insert - <==    Updates: 1
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.social.PostController.createPost, 返回结果: {"code":200,"msg":"动态发布成功","data":3}
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 社交(String), 创建(String), 发布动态(String), /posts/create(String), POST(String), [{"content":"123124","images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"]}](String), {"x-request-id":"L8Ex0j6sLEScUEJELq2RU","sec-fetch-mode":"cors","content-length":"316","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","content-type":"application/json","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 动态发布成功(String), 3(String), 2(Long), 管理员(String), 2025-06-05T09:25:02.087037200(LocalDateTime), 88(Long)
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "动态发布成功", "data": 3}]
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:25:02 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: POST /posts/create, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"316", pragma:"no-cache", cache-control:"no-cache", x-request-id:"L8Ex0j6sLEScUEJELq2RU", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", Content-Type:"application/json;charset=UTF-8"], payload={"content":"123124","images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"]}]
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /posts/list?page=1&pageSize=10&followed=false, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"vAsvWbVaHmeKuFsXaY9Ua", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - GET "/posts/list?page=1&pageSize=10&followed=false", parameters={masked}
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.social.PostController#getPostList(Integer, Integer, Integer, String, Boolean)
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.social.PostController.getPostList
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [1,10,null,null,false]
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.s.m.P.selectPostListWithUser - ==>  Preparing: SELECT p.post_id as id, p.user_id as userId, u.name as userName, u.avatar as userAvatar, p.content, p.images, p.skill_id as skillId, ss.title as skillTitle, p.like_count as likeCount, p.comment_count as commentCount, p.view_count as viewCount, p.created_time as createdTime, CASE WHEN l.like_id IS NOT NULL THEN 1 ELSE 0 END as isLiked, CASE WHEN uf.id IS NOT NULL THEN 1 ELSE 0 END as isFollowed FROM posts p LEFT JOIN users u ON p.user_id = u.user_id LEFT JOIN skill_services ss ON p.skill_id = ss.service_id LEFT JOIN likes l ON l.target_id = p.post_id AND l.target_type = 'post' AND l.user_id = ? LEFT JOIN user_follows uf ON uf.following_id = p.user_id AND uf.follower_id = ? WHERE p.status = 1 ORDER BY p.created_time DESC LIMIT ?, ?
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.s.m.P.selectPostListWithUser - ==> Parameters: 2(Integer), 2(Integer), 0(Integer), 10(Integer)
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.s.m.P.selectPostListWithUser - <==      Total: 3
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.s.m.PostMapper.selectPostCount - ==>  Preparing: SELECT COUNT(*) FROM posts p WHERE p.status = 1
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.s.m.PostMapper.selectPostCount - ==> Parameters: 
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.s.m.PostMapper.selectPostCount - <==      Total: 1
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.social.PostController.getPostList, 返回结果: {"code":200,"msg":"动态列表查询成功","data":{"total":3,"current":1,"pages":1,"size":10,"records":[{"images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"],"isLiked":0,"createdTime":1749086702000,"likeCount":0,"id":3,"viewCount":0,"userName":"管理员","userId":2,"content":"123124","isFollowed":0,"commentCount":0},{"images":["/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg"],"isLiked":0,"createdTime":1749025083000,"likeCount":1,"id":2,"viewCount":31,"userName":"测试账户二","userId":1,"content":"# 权限架构是什么\n权限架构（Permission Architecture）是一种管理和控制系统中用户访问资源的方式。它定义了谁可以访问哪些资源以及如何进行这些访问。权限架构的主要目标是确保系统的安全性和数据的完整性，防止未授权的访问和潜在的安全威胁。\n通俗一点说，对于一个系统，我们需要规定每个用户能干什么，不能干什么，这也就是每个用户的权限，我们需要设置合理的权限去管理每一个接口。\n# 权限架构包含什么\n一个最基础的权限架构通常会包含三个内容，两种，一个是具体的用户，一个是用户组，一个是权限，另一种是，用户，角色，权限。在大部分的系统中，我们通常只会将权限赋予用户组，然后用户属于用户组。\n# 五表结构\n经典的五表结构是指在权限管理系统中常见的五张核心数据表，用于管理用户、角色、权限以及它们之间的关系。这种结构能够清晰地定义和维护权限体系，广泛应用于基于角色的访问控制（RBAC）模型。五表结构通常包括以下五张表：\n## 用户表（users）\n- 记录系统中所有用户的信息。\n- 典型字段：用户ID、用户名、密码、电子邮件、状态（如激活或禁用）等。\n```\nCREATE TABLE users (\n  user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',\n  username VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '用户名',\n  PASSWORD VARCHAR ( 255 ) NOT NULL COMMENT '密码',\n  email VARCHAR ( 255 ) NOT NULL COMMENT '邮箱，用于登录',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '用户表';\n```\n## 角色表（Role）\n- 存储角色的基本信息，如角色ID、角色名、角色描述等。角色是权限的集合，用户通过角色来获得权限。\n```\nCREATE TABLE roles (\n  role_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',\n  role_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '角色名称',\n  description VARCHAR ( 255 ) COMMENT '角色描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '角色表';\n```\n## 权限表（Permission）\n- 存储权限的基本信息，如权限ID、权限名、权限描述等。权限通常对应于系统中的某些操作或资源的访问控制。\n```\nCREATE TABLE permissions (\n  permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',\n  permission_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '权限名称',\n  description VARCHAR ( 255 ) COMMENT '权限描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '权限表';\n```\n## 用户角色关系表（UserRole)：\n- 存储用户和角色之间的关系，通常包括用户ID和角色ID。这个表用来表示一个用户拥有哪些角色。\n```\nCREATE TABLE user_roles (\n  user_id INT COMMENT '用户ID',\n  role_id INT COMMENT '角色ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( user_id, role_id ),\n  FOREIGN KEY ( user_id ) REFERENCES users ( user_id ) ON DELETE CASCADE ON UPDATE CASCADE,\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '用户角色关联表';\n```\n## 角色权限关联表（rolePermissions）\n- 存储权限与角色之间的关系，通常包括权限ID和角色ID。这个表用来表示一个角色拥有哪些权限\n```\nCREATE TABLE role_permissions (\n  role_id INT COMMENT '角色ID',\n  permission_id INT COMMENT '权限ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( role_id, permission_id ),\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE,\nFOREIGN KEY ( permission_id ) REFERENCES permissions ( permission_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '角色权限关联表';\n```\n","isFollowed":0,"commentCount":1},{"images":["/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg","/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg"],"isLiked":0,"createdTime":1749022456000,"likeCount":1,"id":1,"viewCount":26,"userName":"测试账户一","userId":3,"content":"·12412412","isFollowed":0,"commentCount":3}]}}
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 社交(String), 查询(String), 查询动态列表(String), /posts/list(String), GET(String), [1,10,null,null,false](String), {"x-request-id":"vAsvWbVaHmeKuFsXaY9Ua","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 动态列表查询成功(String), {"total":3,"current":1,"pages":1,"size":10,"records":[{"images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"],"isLiked":0,"createdTime":1749086702000,"likeCount":0,"id":3,"viewCount":0,"userName":"管理员","userId":2,"content":"123124","isFollowed":0,"commentCount":0},{"images":["/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg"],"isLiked":0,"createdTime":1749025083000,"likeCount":1,"id":2,"viewCount":31,"userName":"测试账户二","userId":1,"content":"# 权限架构是什么\n权限架构（Permission Architecture）是一种管理和控制系统中用户访问资源的方式。它定义了谁可以访问哪些资源以及如何进行这些访问。权限架构的主要目标是确保系统的安全性和数据的完整性，防止未授权的访问和潜在的安全威胁。\n通俗一点说，对于一个系统，我们需要规定每个用户能干什么，不能干什么，这也就是每个用户的权限，我们需要设置合理的权限去管理每一个接口。\n# 权限架构包含什么\n一个最基础的权限架构通常会包含三个内容，两种，一个是具体的用户，一个是用户组，一个是权限，另一种是，用户，角色，权限。在大部分的系统中，我们通常只会将权限赋予用户组，然后用户属于用户组。\n# 五表结构\n经典的五表结构是指在权限管理系统中常见的五张核心数据表，用于管理用户、角色、权限以及它们之间的关系。这种结构能够清晰地定义和维护权限体系，广泛应用于基于角色的访问控制（RBAC）模型。五表结构通常包括以下五张表：\n## 用户表（users）\n- 记录系统中所有用户的信息。\n- 典型字段：用户ID、用户名、密码、电子邮件、状态（如激活或禁用）等。\n```\nCREATE TABLE users (\n  user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',\n  username VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '用户名',\n  PASSWORD VARCHAR ( 255 ) NOT NULL COMMENT '密码',\n  email VARCHAR ( 255 ) NOT NULL COMMENT '邮箱，用于登录',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '用户表';\n```\n## 角色表（Role）\n- 存储角色的基本信息，如角色ID、角色名、角色描述等。角色是权限的集合，用户通过角色来获得权限。\n```\nCREATE TABLE roles (\n  role_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',\n  role_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '角色名称',\n  description VARCHAR ( 255 ) COMMENT '角色描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '角色表';\n```\n## 权限表（Permission）\n- 存储权限的基本信息，如权限ID、权限名、权限描述等。权限通常对应于系统中的某些操作或资源的访问控制。\n```\nCREATE TABLE permissions (\n  permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',\n  permission_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '权限名称',\n  description VARCHAR ( 255 ) COMMENT '权限描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '权限表';\n```\n## 用户角色关系表（UserRole)：\n- 存储用户和角色之间的关系，通常包括用户ID和角色ID。这个表用来表示一个用户拥有哪些角色。\n```\nCREATE TABLE user_roles (\n  user_id INT COMMENT '用户ID',\n  role_id INT COMMENT '角色ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( user_id, role_id ),\n  FOREIGN KEY ( user_id ) REFERENCES users ( user_id ) ON DELETE CASCADE ON UPDATE CASCADE,\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '用户角色关联表';\n```\n## 角色权限关联表（rolePermissions）\n- 存储权限与角色之间的关系，通常包括权限ID和角色ID。这个表用来表示一个角色拥有哪些权限\n```\nCREATE TABLE role_permissions (\n  role_id INT COMMENT '角色ID',\n  permission_id INT COMMENT '权限ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( role_id, permission_id ),\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE,\nFOREIGN KEY ( permission_id ) REFERENCES permissions ( permission_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '角色权限关联表';\n```\n","isFollowed":0,"commentCount":1},{"images":["/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg","/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg"],"isLiked":0,"createdTime":1749022456000,"likeCount":1,"id":1,"viewCount":26,"userName":"测试账户一","userId":3,"content":"·12412412","isFollowed":0,"commentCount":3}]}(String), 2(Long), 管理员(String), 2025-06-05T09:25:02.202420900(LocalDateTime), 46(Long)
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "动态列表查询成功", "data": {total=3, current=1, pages=1, size=10, records=[{images=[/f (truncated)...]
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:25:02 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /posts/list?page=1&pageSize=10&followed=false, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"vAsvWbVaHmeKuFsXaY9Ua", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:02 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:02 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:02 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg", parameters={}
2025-06-05 09:25:02 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg", parameters={}
2025-06-05 09:25:02 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:02 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg", parameters={}
2025-06-05 09:25:02 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:02 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp", parameters={}
2025-06-05 09:25:02 [http-nio-8081-exec-7] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:25:02 [http-nio-8081-exec-9] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:25:02 [http-nio-8081-exec-5] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:25:02 [http-nio-8081-exec-4] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:25:02 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:25:02 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:02 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:25:02 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:02 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:25:02 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:02 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:25:02 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"KZOLp9HT59CTeDrbdsegk", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"bLOr-dkZ_G-rnRhySn6fB", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"bLOr-dkZ_G-rnRhySn6fB","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:25:18.511863300(LocalDateTime), 25(Long)
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"KZOLp9HT59CTeDrbdsegk","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:25:18.509864500(LocalDateTime), 24(Long)
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:25:18 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"bLOr-dkZ_G-rnRhySn6fB", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:25:18 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"KZOLp9HT59CTeDrbdsegk", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:36 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [POST /admin/backup/create, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"0", pragma:"no-cache", cache-control:"no-cache", x-request-id:"e0VqSqg5HJT2uRObUVKZg", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:25:36 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - POST "/admin/backup/create", parameters={}
2025-06-05 09:25:36 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.admin.AdminController#createSystemBackup()
2025-06-05 09:25:36 [http-nio-8081-exec-6] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:25:36 [http-nio-8081-exec-6] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:25:36 [http-nio-8081-exec-6] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:25:36 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.admin.AdminController.createSystemBackup
2025-06-05 09:25:36 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:25:36 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:25:36 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:25:36 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:25:36 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:25:36 [http-nio-8081-exec-6] INFO  c.s.s.impl.SystemBackupServiceImpl - 开始创建系统备份...
2025-06-05 09:25:41 [http-nio-8081-exec-6] INFO  c.s.s.impl.SystemBackupServiceImpl - 数据库导出完成: /files/backup/20250605_092541.sql
2025-06-05 09:25:44 [http-nio-8081-exec-6] INFO  c.s.s.impl.SystemBackupServiceImpl - 文件打包完成: /files/backup/20250605_092544.zip
2025-06-05 09:25:46 [http-nio-8081-exec-6] INFO  c.s.s.impl.SystemBackupServiceImpl - 系统备份创建完成: /files/backup/20250605_092546.zip
2025-06-05 09:25:46 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.admin.AdminController.createSystemBackup, 返回结果: {"code":200,"msg":"系统备份创建成功","data":"/files/backup/20250605_092546.zip"}
2025-06-05 09:25:46 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:25:46 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 系统管理(String), 备份(String), 创建系统备份(String), /admin/backup/create(String), POST(String), [](String), {"x-request-id":"e0VqSqg5HJT2uRObUVKZg","sec-fetch-mode":"cors","content-length":"0","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 系统备份创建成功(String), "/files/backup/20250605_092546.zip"(String), 2(Long), 管理员(String), 2025-06-05T09:25:46.349328900(LocalDateTime), 9506(Long)
2025-06-05 09:25:46 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:25:46 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:25:46 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:25:46 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "系统备份创建成功", "data": "/files/backup/20250605_092546.zip"}]
2025-06-05 09:25:46 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:25:46 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: POST /admin/backup/create, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"0", pragma:"no-cache", cache-control:"no-cache", x-request-id:"e0VqSqg5HJT2uRObUVKZg", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:26:43 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/backup/20250605_092546.zip, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", sec-ch-ua-platform:""Windows"", upgrade-insecure-requests:"1", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", sec-fetch-site:"cross-site", sec-fetch-mode:"navigate", sec-fetch-user:"?1", sec-fetch-dest:"document", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", cookie:"agh_session=d8386b3b9423d0e75506e51d30b3a7e7; csrftoken=2GlyhZ7qrmQc2PXyRnHMqpSNailbIfBp; accessToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE2NDY4Mzg2MjUsInJuU3RyIjoiNURHUVVCZGxSbG5KZUNUb3BSdEZmeE5ZSnZSWG14elEiLCJuYW1lIjoi566h55CGIn0.e0WYATw8Bq3mZywnPhB22nrZvRJls2OczMndumyeFBk"]]
2025-06-05 09:26:43 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/backup/20250605_092546.zip", parameters={}
2025-06-05 09:26:43 [http-nio-8081-exec-2] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:26:43 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:26:43 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/backup/20250605_092546.zip, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", sec-ch-ua-platform:""Windows"", upgrade-insecure-requests:"1", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", sec-fetch-site:"cross-site", sec-fetch-mode:"navigate", sec-fetch-user:"?1", sec-fetch-dest:"document", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", cookie:"agh_session=d8386b3b9423d0e75506e51d30b3a7e7; csrftoken=2GlyhZ7qrmQc2PXyRnHMqpSNailbIfBp; accessToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE2NDY4Mzg2MjUsInJuU3RyIjoiNURHUVVCZGxSbG5KZUNUb3BSdEZmeE5ZSnZSWG14elEiLCJuYW1lIjoi566h55CGIn0.e0WYATw8Bq3mZywnPhB22nrZvRJls2OczMndumyeFBk"]]
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [POST /user/login, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"58", pragma:"no-cache", cache-control:"no-cache", x-request-id:"FFvZ0EWpBMudBQRHM2J9v", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", Content-Type:"application/json;charset=UTF-8"]]
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - POST "/user/login", parameters={}
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#login(LoginDto)
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [LoginDto(email=<EMAIL>, password=123456)]
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.login
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [{"email":"<EMAIL>","password":"123456"}]
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectList - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE (email = ?)
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectList - ==> Parameters: <EMAIL>(String)
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectList - <==      Total: 1
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.updateById - ==>  Preparing: UPDATE users SET last_login_time=? WHERE user_id=?
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.updateById - ==> Parameters: 2025-06-05 09:43:01.78(Timestamp), 1(Integer)
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.updateById - <==    Updates: 1
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 1(String)
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:43:01 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.login, 返回结果: {"code":200,"msg":"ok","data":{"userInfo":{"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false},"isFirstLogin":true,"tokenInfo":{"tokenName":"satoken","tokenValue":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","isLogin":true,"loginId":"1","loginType":"login","tokenTimeout":2592000,"sessionTimeout":2592000,"tokenSessionTimeout":-2,"tokenActiveTimeout":-1,"loginDeviceType":"DEF","tag":null}}}
2025-06-05 09:43:02 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:02 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:02 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:02 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:02 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 登录(String), 用户登录(String), /user/login(String), POST(String), [{"email":"<EMAIL>","password":"123456"}](String), {"x-request-id":"FFvZ0EWpBMudBQRHM2J9v","sec-fetch-mode":"cors","content-length":"58","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","content-type":"application/json","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userInfo":{"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false},"isFirstLogin":true,"tokenInfo":{"tokenName":"satoken","tokenValue":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","isLogin":true,"loginId":"1","loginType":"login","tokenTimeout":2592000,"sessionTimeout":2592000,"tokenSessionTimeout":-2,"tokenActiveTimeout":-1,"loginDeviceType":"DEF","tag":null}}(String), 1(Long), 测试账户二(String), 2025-06-05T09:43:02.215618300(LocalDateTime), 203(Long)
2025-06-05 09:43:02 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:02 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:02 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:02 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": {userInfo=UserInfoVo(userId=1, name=测试账户二, stuId=220911123, acade (truncated)...]
2025-06-05 09:43:02 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:02 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: POST /user/login, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"58", pragma:"no-cache", cache-control:"no-cache", x-request-id:"FFvZ0EWpBMudBQRHM2J9v", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", Content-Type:"application/json;charset=UTF-8"], payload={"email":"<EMAIL>","password":"123456"}]
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"ANFLIVG-yqy0cqisntU2n", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 1(String)
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}}
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"ANFLIVG-yqy0cqisntU2n","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}(String), 1(Long), 测试账户二(String), 2025-06-05T09:43:02.260530(LocalDateTime), 17(Long)
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=1, name=测试账户二, stuId=220911123, academy=1111, e (truncated)...]
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:02 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"ANFLIVG-yqy0cqisntU2n", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"BCkWXOg559D0Ma1FSsItm", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"eNUnP-pdQ2zaHNsVTa0fr", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 1(String)
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 1(String)
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}}
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}}
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"BCkWXOg559D0Ma1FSsItm","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}(String), 1(Long), 测试账户二(String), 2025-06-05T09:43:22.950938400(LocalDateTime), 21(Long)
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"eNUnP-pdQ2zaHNsVTa0fr","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}(String), 1(Long), 测试账户二(String), 2025-06-05T09:43:22.950938400(LocalDateTime), 21(Long)
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=1, name=测试账户二, stuId=220911123, academy=1111, e (truncated)...]
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=1, name=测试账户二, stuId=220911123, academy=1111, e (truncated)...]
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:22 [http-nio-8081-exec-1] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"eNUnP-pdQ2zaHNsVTa0fr", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:22 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"BCkWXOg559D0Ma1FSsItm", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /wallet/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"Kdz299hFhVvc9p03B5ZeV", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/wallet/info", parameters={}
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.wallet.WalletController#getWalletInfo()
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.wallet.WalletController.getWalletInfo
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /wallet/transactions?startTime=&endTime=&page=1&size=10, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"E7oUWxtu-IGdWluXfacr0", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/wallet/transactions?startTime=&endTime=&page=1&size=10", parameters={masked}
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.wallet.WalletController#getTransactions(Integer, Integer, Integer, Integer, Date, Date)
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.wallet.WalletController.getTransactions
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [1,10,null,null,null,null]
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG c.s.m.WalletMapper.selectByUserId - ==>  Preparing: SELECT * FROM wallets WHERE user_id = ?
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG c.s.m.WalletMapper.selectByUserId - ==> Parameters: 1(Integer)
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG c.s.m.W.selectUserTransactions - ==>  Preparing: SELECT * FROM transactions WHERE user_id = ? ORDER BY created_time DESC
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG c.s.m.WalletMapper.selectByUserId - <==      Total: 1
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG c.s.m.W.selectUserTransactions - ==> Parameters: 1(Integer)
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.wallet.WalletController.getWalletInfo, 返回结果: {"code":200,"msg":"查询成功","data":{"updatedTime":1748973501000,"balance":110.00,"id":2,"userId":1,"frozenAmount":0.00}}
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 钱包管理(String), 查看钱包(String), 查看钱包信息(String), /wallet/info(String), GET(String), [](String), {"x-request-id":"Kdz299hFhVvc9p03B5ZeV","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 查询成功(String), {"updatedTime":1748973501000,"balance":110.00,"id":2,"userId":1,"frozenAmount":0.00}(String), 1(Long), 测试账户二(String), 2025-06-05T09:43:27.154757600(LocalDateTime), 36(Long)
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG c.s.m.W.selectUserTransactions - <==      Total: 2
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.wallet.WalletController.getTransactions, 返回结果: {"code":200,"msg":"查询成功","data":{"total":0,"current":1,"pages":0,"size":10,"records":[{"transactionId":2,"transactionNo":"TXN202506040958219790","userId":1,"type":0,"amount":100.00,"balanceBefore":10.00,"balanceAfter":110.00,"status":1,"relatedId":null,"relatedType":"recharge","remark":"微信支付充值","createdTime":1749002302000,"updatedTime":1749002302000},{"transactionId":1,"transactionNo":"TXN202506040942301408","userId":1,"type":0,"amount":10.00,"balanceBefore":0.00,"balanceAfter":10.00,"status":1,"relatedId":null,"relatedType":"recharge","remark":"支付宝支付充值","createdTime":1749001350000,"updatedTime":1749001350000}]}}
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 钱包管理(String), 查看交易记录(String), 查看交易记录(String), /wallet/transactions(String), GET(String), [1,10,null,null,null,null](String), {"x-request-id":"E7oUWxtu-IGdWluXfacr0","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 查询成功(String), {"total":0,"current":1,"pages":0,"size":10,"records":[{"transactionId":2,"transactionNo":"TXN202506040958219790","userId":1,"type":0,"amount":100.00,"balanceBefore":10.00,"balanceAfter":110.00,"status":1,"relatedId":null,"relatedType":"recharge","remark":"微信支付充值","createdTime":1749002302000,"updatedTime":1749002302000},{"transactionId":1,"transactionNo":"TXN202506040942301408","userId":1,"type":0,"amount":10.00,"balanceBefore":0.00,"balanceAfter":10.00,"status":1,"relatedId":null,"relatedType":"recharge","remark":"支付宝支付充值","createdTime":1749001350000,"updatedTime":1749001350000}]}(String), 1(Long), 测试账户二(String), 2025-06-05T09:43:27.162864(LocalDateTime), 33(Long)
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "查询成功", "data": {updatedTime=Wed Jun 04 01:58:21 CST 2025, balance=110.00, id=2 (truncated)...]
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:27 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /wallet/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"Kdz299hFhVvc9p03B5ZeV", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "查询成功", "data": {total=0, current=1, pages=0, size=10, records=[WalletTransacti (truncated)...]
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:27 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /wallet/transactions?startTime=&endTime=&page=1&size=10, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"E7oUWxtu-IGdWluXfacr0", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"aRlxs4nxRwTd6cuIP5DOg", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /posts/list?page=1&pageSize=10&followed=false, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"OPSy-SlXaX60ln0s9wQiL", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/posts/list?page=1&pageSize=10&followed=false", parameters={masked}
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.social.PostController#getPostList(Integer, Integer, Integer, String, Boolean)
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /social/posts/hot?limit=5, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"8WLE4nfjunGjI6y2q4hCV", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /social/users/recommended?limit=5, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"ftdgDVttPqDw9oec3B2Fp", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/social/posts/hot?limit=5", parameters={masked}
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - GET "/social/users/recommended?limit=5", parameters={masked}
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.social.SocialController#getHotPosts(Integer)
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.social.SocialController#getRecommendedUsers(Integer)
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.social.PostController.getPostList
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [1,10,null,null,false]
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.social.SocialController.getRecommendedUsers
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.social.SocialController.getHotPosts
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [5]
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [5]
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG c.s.mapper.PostMapper.selectHotPosts - ==>  Preparing: SELECT p.post_id as id, p.user_id as userId, u.name as userName, u.avatar as userAvatar, u.user_type as userType, p.content, p.images, p.skill_id as skillId, ss.title as skillTitle, p.like_count as likeCount, p.comment_count as commentCount, p.view_count as viewCount, p.created_time as createdTime, CASE WHEN l.like_id IS NOT NULL THEN 1 ELSE 0 END as isLiked, CASE WHEN uf.id IS NOT NULL THEN 1 ELSE 0 END as isFollowed FROM posts p LEFT JOIN users u ON p.user_id = u.user_id LEFT JOIN skill_services ss ON p.skill_id = ss.service_id LEFT JOIN likes l ON l.target_id = p.post_id AND l.target_type = 'post' AND l.user_id = ? LEFT JOIN user_follows uf ON uf.following_id = p.user_id AND uf.follower_id = ? WHERE p.status = 1 ORDER BY p.like_count DESC, p.created_time DESC LIMIT ?
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG c.s.m.U.selectRecommendedUsers - ==>  Preparing: SELECT u.user_id as userId, u.name as userName, u.avatar as userAvatar, u.user_type as userType, COALESCE(follower_counts.followers_count, 0) as followersCount, CASE WHEN uf.id IS NOT NULL THEN 1 ELSE 0 END as isFollowed FROM users u LEFT JOIN ( SELECT following_id, COUNT(*) as followers_count FROM user_follows GROUP BY following_id ) follower_counts ON u.user_id = follower_counts.following_id LEFT JOIN user_follows uf ON uf.follower_id = ? AND uf.following_id = u.user_id WHERE u.user_id != ? AND u.status = 1 ORDER BY follower_counts.followers_count DESC, u.user_id ASC LIMIT ?
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.s.m.P.selectPostListWithUser - ==>  Preparing: SELECT p.post_id as id, p.user_id as userId, u.name as userName, u.avatar as userAvatar, p.content, p.images, p.skill_id as skillId, ss.title as skillTitle, p.like_count as likeCount, p.comment_count as commentCount, p.view_count as viewCount, p.created_time as createdTime, CASE WHEN l.like_id IS NOT NULL THEN 1 ELSE 0 END as isLiked, CASE WHEN uf.id IS NOT NULL THEN 1 ELSE 0 END as isFollowed FROM posts p LEFT JOIN users u ON p.user_id = u.user_id LEFT JOIN skill_services ss ON p.skill_id = ss.service_id LEFT JOIN likes l ON l.target_id = p.post_id AND l.target_type = 'post' AND l.user_id = ? LEFT JOIN user_follows uf ON uf.following_id = p.user_id AND uf.follower_id = ? WHERE p.status = 1 ORDER BY p.created_time DESC LIMIT ?, ?
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG c.s.mapper.PostMapper.selectHotPosts - ==> Parameters: 1(Integer), 1(Integer), 5(Integer)
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG c.s.m.U.selectRecommendedUsers - ==> Parameters: 1(Integer), 1(Integer), 5(Integer)
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.s.m.P.selectPostListWithUser - ==> Parameters: 1(Integer), 1(Integer), 0(Integer), 10(Integer)
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG c.s.m.U.selectRecommendedUsers - <==      Total: 1
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.social.SocialController.getRecommendedUsers, 返回结果: {"code":200,"msg":"查询成功","data":[{"userType":1,"followersCount":1,"userName":"测试账户一","userId":3,"isFollowed":1}]}
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 1(String)
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.s.m.P.selectPostListWithUser - <==      Total: 3
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.s.m.PostMapper.selectPostCount - ==>  Preparing: SELECT COUNT(*) FROM posts p WHERE p.status = 1
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.s.m.PostMapper.selectPostCount - ==> Parameters: 
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG c.s.mapper.PostMapper.selectHotPosts - <==      Total: 3
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 社交管理(String), 查看推荐用户(String), 获取推荐用户列表(String), /social/users/recommended(String), GET(String), [5](String), {"x-request-id":"ftdgDVttPqDw9oec3B2Fp","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 查询成功(String), [{"userType":1,"followersCount":1,"userName":"测试账户一","userId":3,"isFollowed":1}](String), 1(Long), 测试账户二(String), 2025-06-05T09:43:29.858057(LocalDateTime), 14(Long)
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.social.SocialController.getHotPosts, 返回结果: {"code":200,"msg":"查询成功","data":[{"images":["/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg"],"isLiked":1,"createdTime":1749025083000,"likeCount":1,"id":2,"userType":1,"viewCount":31,"userName":"测试账户二","userId":1,"content":"# 权限架构是什么\n权限架构（Permission Architecture）是一种管理和控制系统中用户访问资源的方式。它定义了谁可以访问哪些资源以及如何进行这些访问。权限架构的主要目标是确保系统的安全性和数据的完整性，防止未授权的访问和潜在的安全威胁。\n通俗一点说，对于一个系统，我们需要规定每个用户能干什么，不能干什么，这也就是每个用户的权限，我们需要设置合理的权限去管理每一个接口。\n# 权限架构包含什么\n一个最基础的权限架构通常会包含三个内容，两种，一个是具体的用户，一个是用户组，一个是权限，另一种是，用户，角色，权限。在大部分的系统中，我们通常只会将权限赋予用户组，然后用户属于用户组。\n# 五表结构\n经典的五表结构是指在权限管理系统中常见的五张核心数据表，用于管理用户、角色、权限以及它们之间的关系。这种结构能够清晰地定义和维护权限体系，广泛应用于基于角色的访问控制（RBAC）模型。五表结构通常包括以下五张表：\n## 用户表（users）\n- 记录系统中所有用户的信息。\n- 典型字段：用户ID、用户名、密码、电子邮件、状态（如激活或禁用）等。\n```\nCREATE TABLE users (\n  user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',\n  username VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '用户名',\n  PASSWORD VARCHAR ( 255 ) NOT NULL COMMENT '密码',\n  email VARCHAR ( 255 ) NOT NULL COMMENT '邮箱，用于登录',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '用户表';\n```\n## 角色表（Role）\n- 存储角色的基本信息，如角色ID、角色名、角色描述等。角色是权限的集合，用户通过角色来获得权限。\n```\nCREATE TABLE roles (\n  role_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',\n  role_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '角色名称',\n  description VARCHAR ( 255 ) COMMENT '角色描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '角色表';\n```\n## 权限表（Permission）\n- 存储权限的基本信息，如权限ID、权限名、权限描述等。权限通常对应于系统中的某些操作或资源的访问控制。\n```\nCREATE TABLE permissions (\n  permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',\n  permission_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '权限名称',\n  description VARCHAR ( 255 ) COMMENT '权限描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '权限表';\n```\n## 用户角色关系表（UserRole)：\n- 存储用户和角色之间的关系，通常包括用户ID和角色ID。这个表用来表示一个用户拥有哪些角色。\n```\nCREATE TABLE user_roles (\n  user_id INT COMMENT '用户ID',\n  role_id INT COMMENT '角色ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( user_id, role_id ),\n  FOREIGN KEY ( user_id ) REFERENCES users ( user_id ) ON DELETE CASCADE ON UPDATE CASCADE,\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '用户角色关联表';\n```\n## 角色权限关联表（rolePermissions）\n- 存储权限与角色之间的关系，通常包括权限ID和角色ID。这个表用来表示一个角色拥有哪些权限\n```\nCREATE TABLE role_permissions (\n  role_id INT COMMENT '角色ID',\n  permission_id INT COMMENT '权限ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( role_id, permission_id ),\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE,\nFOREIGN KEY ( permission_id ) REFERENCES permissions ( permission_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '角色权限关联表';\n```\n","isFollowed":0,"commentCount":1},{"images":["/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg","/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg"],"isLiked":1,"createdTime":1749022456000,"likeCount":1,"id":1,"userType":1,"viewCount":26,"userName":"测试账户一","userId":3,"content":"·12412412","isFollowed":1,"commentCount":3},{"images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"],"isLiked":0,"createdTime":1749086702000,"likeCount":0,"id":3,"userType":1,"viewCount":0,"userName":"管理员","userId":2,"content":"123124","isFollowed":0,"commentCount":0}]}
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.s.m.PostMapper.selectPostCount - <==      Total: 1
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}}
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.social.PostController.getPostList, 返回结果: {"code":200,"msg":"动态列表查询成功","data":{"total":3,"current":1,"pages":1,"size":10,"records":[{"images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"],"isLiked":0,"createdTime":1749086702000,"likeCount":0,"id":3,"viewCount":0,"userName":"管理员","userId":2,"content":"123124","isFollowed":0,"commentCount":0},{"images":["/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg"],"isLiked":1,"createdTime":1749025083000,"likeCount":1,"id":2,"viewCount":31,"userName":"测试账户二","userId":1,"content":"# 权限架构是什么\n权限架构（Permission Architecture）是一种管理和控制系统中用户访问资源的方式。它定义了谁可以访问哪些资源以及如何进行这些访问。权限架构的主要目标是确保系统的安全性和数据的完整性，防止未授权的访问和潜在的安全威胁。\n通俗一点说，对于一个系统，我们需要规定每个用户能干什么，不能干什么，这也就是每个用户的权限，我们需要设置合理的权限去管理每一个接口。\n# 权限架构包含什么\n一个最基础的权限架构通常会包含三个内容，两种，一个是具体的用户，一个是用户组，一个是权限，另一种是，用户，角色，权限。在大部分的系统中，我们通常只会将权限赋予用户组，然后用户属于用户组。\n# 五表结构\n经典的五表结构是指在权限管理系统中常见的五张核心数据表，用于管理用户、角色、权限以及它们之间的关系。这种结构能够清晰地定义和维护权限体系，广泛应用于基于角色的访问控制（RBAC）模型。五表结构通常包括以下五张表：\n## 用户表（users）\n- 记录系统中所有用户的信息。\n- 典型字段：用户ID、用户名、密码、电子邮件、状态（如激活或禁用）等。\n```\nCREATE TABLE users (\n  user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',\n  username VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '用户名',\n  PASSWORD VARCHAR ( 255 ) NOT NULL COMMENT '密码',\n  email VARCHAR ( 255 ) NOT NULL COMMENT '邮箱，用于登录',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '用户表';\n```\n## 角色表（Role）\n- 存储角色的基本信息，如角色ID、角色名、角色描述等。角色是权限的集合，用户通过角色来获得权限。\n```\nCREATE TABLE roles (\n  role_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',\n  role_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '角色名称',\n  description VARCHAR ( 255 ) COMMENT '角色描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '角色表';\n```\n## 权限表（Permission）\n- 存储权限的基本信息，如权限ID、权限名、权限描述等。权限通常对应于系统中的某些操作或资源的访问控制。\n```\nCREATE TABLE permissions (\n  permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',\n  permission_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '权限名称',\n  description VARCHAR ( 255 ) COMMENT '权限描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '权限表';\n```\n## 用户角色关系表（UserRole)：\n- 存储用户和角色之间的关系，通常包括用户ID和角色ID。这个表用来表示一个用户拥有哪些角色。\n```\nCREATE TABLE user_roles (\n  user_id INT COMMENT '用户ID',\n  role_id INT COMMENT '角色ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( user_id, role_id ),\n  FOREIGN KEY ( user_id ) REFERENCES users ( user_id ) ON DELETE CASCADE ON UPDATE CASCADE,\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '用户角色关联表';\n```\n## 角色权限关联表（rolePermissions）\n- 存储权限与角色之间的关系，通常包括权限ID和角色ID。这个表用来表示一个角色拥有哪些权限\n```\nCREATE TABLE role_permissions (\n  role_id INT COMMENT '角色ID',\n  permission_id INT COMMENT '权限ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( role_id, permission_id ),\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE,\nFOREIGN KEY ( permission_id ) REFERENCES permissions ( permission_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '角色权限关联表';\n```\n","isFollowed":0,"commentCount":1},{"images":["/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg","/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg"],"isLiked":1,"createdTime":1749022456000,"likeCount":1,"id":1,"viewCount":26,"userName":"测试账户一","userId":3,"content":"·12412412","isFollowed":1,"commentCount":3}]}}
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"aRlxs4nxRwTd6cuIP5DOg","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}(String), 1(Long), 测试账户二(String), 2025-06-05T09:43:29.862465300(LocalDateTime), 19(Long)
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 社交管理(String), 查看热门帖子(String), 获取热门帖子列表(String), /social/posts/hot(String), GET(String), [5](String), {"x-request-id":"8WLE4nfjunGjI6y2q4hCV","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 查询成功(String), [{"images":["/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg"],"isLiked":1,"createdTime":1749025083000,"likeCount":1,"id":2,"userType":1,"viewCount":31,"userName":"测试账户二","userId":1,"content":"# 权限架构是什么\n权限架构（Permission Architecture）是一种管理和控制系统中用户访问资源的方式。它定义了谁可以访问哪些资源以及如何进行这些访问。权限架构的主要目标是确保系统的安全性和数据的完整性，防止未授权的访问和潜在的安全威胁。\n通俗一点说，对于一个系统，我们需要规定每个用户能干什么，不能干什么，这也就是每个用户的权限，我们需要设置合理的权限去管理每一个接口。\n# 权限架构包含什么\n一个最基础的权限架构通常会包含三个内容，两种，一个是具体的用户，一个是用户组，一个是权限，另一种是，用户，角色，权限。在大部分的系统中，我们通常只会将权限赋予用户组，然后用户属于用户组。\n# 五表结构\n经典的五表结构是指在权限管理系统中常见的五张核心数据表，用于管理用户、角色、权限以及它们之间的关系。这种结构能够清晰地定义和维护权限体系，广泛应用于基于角色的访问控制（RBAC）模型。五表结构通常包括以下五张表：\n## 用户表（users）\n- 记录系统中所有用户的信息。\n- 典型字段：用户ID、用户名、密码、电子邮件、状态（如激活或禁用）等。\n```\nCREATE TABLE users (\n  user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',\n  username VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '用户名',\n  PASSWORD VARCHAR ( 255 ) NOT NULL COMMENT '密码',\n  email VARCHAR ( 255 ) NOT NULL COMMENT '邮箱，用于登录',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '用户表';\n```\n## 角色表（Role）\n- 存储角色的基本信息，如角色ID、角色名、角色描述等。角色是权限的集合，用户通过角色来获得权限。\n```\nCREATE TABLE roles (\n  role_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',\n  role_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '角色名称',\n  description VARCHAR ( 255 ) COMMENT '角色描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '角色表';\n```\n## 权限表（Permission）\n- 存储权限的基本信息，如权限ID、权限名、权限描述等。权限通常对应于系统中的某些操作或资源的访问控制。\n```\nCREATE TABLE permissions (\n  permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',\n  permission_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '权限名称',\n  description VARCHAR ( 255 ) COMMENT '权限描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '权限表';\n```\n## 用户角色关系表（UserRole)：\n- 存储用户和角色之间的关系，通常包括用户ID和角色ID。这个表用来表示一个用户拥有哪些角色。\n```\nCREATE TABLE user_roles (\n  user_id INT COMMENT '用户ID',\n  role_id INT COMMENT '角色ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( user_id, role_id ),\n  FOREIGN KEY ( user_id ) REFERENCES users ( user_id ) ON DELETE CASCADE ON UPDATE CASCADE,\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '用户角色关联表';\n```\n## 角色权限关联表（rolePermissions）\n- 存储权限与角色之间的关系，通常包括权限ID和角色ID。这个表用来表示一个角色拥有哪些权限\n```\nCREATE TABLE role_permissions (\n  role_id INT COMMENT '角色ID',\n  permission_id INT COMMENT '权限ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( role_id, permission_id ),\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE,\nFOREIGN KEY ( permission_id ) REFERENCES permissions ( permission_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '角色权限关联表';\n```\n","isFollowed":0,"commentCount":1},{"images":["/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg","/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg"],"isLiked":1,"createdTime":1749022456000,"likeCount":1,"id":1,"userType":1,"viewCount":26,"userName":"测试账户一","userId":3,"content":"·12412412","isFollowed":1,"commentCount":3},{"images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"],"isLiked":0,"createdTime":1749086702000,"likeCount":0,"id":3,"userType":1,"viewCount":0,"userName":"管理员","userId":2,"content":"123124","isFollowed":0,"commentCount":0}](String), 1(Long), 测试账户二(String), 2025-06-05T09:43:29.860910800(LocalDateTime), 16(Long)
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 社交(String), 查询(String), 查询动态列表(String), /posts/list(String), GET(String), [1,10,null,null,false](String), {"x-request-id":"OPSy-SlXaX60ln0s9wQiL","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 动态列表查询成功(String), {"total":3,"current":1,"pages":1,"size":10,"records":[{"images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"],"isLiked":0,"createdTime":1749086702000,"likeCount":0,"id":3,"viewCount":0,"userName":"管理员","userId":2,"content":"123124","isFollowed":0,"commentCount":0},{"images":["/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg"],"isLiked":1,"createdTime":1749025083000,"likeCount":1,"id":2,"viewCount":31,"userName":"测试账户二","userId":1,"content":"# 权限架构是什么\n权限架构（Permission Architecture）是一种管理和控制系统中用户访问资源的方式。它定义了谁可以访问哪些资源以及如何进行这些访问。权限架构的主要目标是确保系统的安全性和数据的完整性，防止未授权的访问和潜在的安全威胁。\n通俗一点说，对于一个系统，我们需要规定每个用户能干什么，不能干什么，这也就是每个用户的权限，我们需要设置合理的权限去管理每一个接口。\n# 权限架构包含什么\n一个最基础的权限架构通常会包含三个内容，两种，一个是具体的用户，一个是用户组，一个是权限，另一种是，用户，角色，权限。在大部分的系统中，我们通常只会将权限赋予用户组，然后用户属于用户组。\n# 五表结构\n经典的五表结构是指在权限管理系统中常见的五张核心数据表，用于管理用户、角色、权限以及它们之间的关系。这种结构能够清晰地定义和维护权限体系，广泛应用于基于角色的访问控制（RBAC）模型。五表结构通常包括以下五张表：\n## 用户表（users）\n- 记录系统中所有用户的信息。\n- 典型字段：用户ID、用户名、密码、电子邮件、状态（如激活或禁用）等。\n```\nCREATE TABLE users (\n  user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',\n  username VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '用户名',\n  PASSWORD VARCHAR ( 255 ) NOT NULL COMMENT '密码',\n  email VARCHAR ( 255 ) NOT NULL COMMENT '邮箱，用于登录',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '用户表';\n```\n## 角色表（Role）\n- 存储角色的基本信息，如角色ID、角色名、角色描述等。角色是权限的集合，用户通过角色来获得权限。\n```\nCREATE TABLE roles (\n  role_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',\n  role_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '角色名称',\n  description VARCHAR ( 255 ) COMMENT '角色描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '角色表';\n```\n## 权限表（Permission）\n- 存储权限的基本信息，如权限ID、权限名、权限描述等。权限通常对应于系统中的某些操作或资源的访问控制。\n```\nCREATE TABLE permissions (\n  permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',\n  permission_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '权限名称',\n  description VARCHAR ( 255 ) COMMENT '权限描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '权限表';\n```\n## 用户角色关系表（UserRole)：\n- 存储用户和角色之间的关系，通常包括用户ID和角色ID。这个表用来表示一个用户拥有哪些角色。\n```\nCREATE TABLE user_roles (\n  user_id INT COMMENT '用户ID',\n  role_id INT COMMENT '角色ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( user_id, role_id ),\n  FOREIGN KEY ( user_id ) REFERENCES users ( user_id ) ON DELETE CASCADE ON UPDATE CASCADE,\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '用户角色关联表';\n```\n## 角色权限关联表（rolePermissions）\n- 存储权限与角色之间的关系，通常包括权限ID和角色ID。这个表用来表示一个角色拥有哪些权限\n```\nCREATE TABLE role_permissions (\n  role_id INT COMMENT '角色ID',\n  permission_id INT COMMENT '权限ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( role_id, permission_id ),\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE,\nFOREIGN KEY ( permission_id ) REFERENCES permissions ( permission_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '角色权限关联表';\n```\n","isFollowed":0,"commentCount":1},{"images":["/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg","/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg"],"isLiked":1,"createdTime":1749022456000,"likeCount":1,"id":1,"viewCount":26,"userName":"测试账户一","userId":3,"content":"·12412412","isFollowed":1,"commentCount":3}]}(String), 1(Long), 测试账户二(String), 2025-06-05T09:43:29.862970100(LocalDateTime), 19(Long)
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "查询成功", "data": [{userType=1, followersCount=1, userName=测试账户一, userId=3, isFol (truncated)...]
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:29 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /social/users/recommended?limit=5, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"ftdgDVttPqDw9oec3B2Fp", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=1, name=测试账户二, stuId=220911123, academy=1111, e (truncated)...]
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "查询成功", "data": [{images=[/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d202 (truncated)...]
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "动态列表查询成功", "data": {total=3, current=1, pages=1, size=10, records=[{images=[/f (truncated)...]
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:29 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /posts/list?page=1&pageSize=10&followed=false, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"OPSy-SlXaX60ln0s9wQiL", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /social/posts/hot?limit=5, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"8WLE4nfjunGjI6y2q4hCV", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-1] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"aRlxs4nxRwTd6cuIP5DOg", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg", parameters={}
2025-06-05 09:43:29 [http-nio-8081-exec-5] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:29 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp", parameters={}
2025-06-05 09:43:29 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg", parameters={}
2025-06-05 09:43:29 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg", parameters={}
2025-06-05 09:43:29 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg", parameters={}
2025-06-05 09:43:29 [http-nio-8081-exec-4] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:29 [http-nio-8081-exec-10] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:29 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-3] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:29 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg", parameters={}
2025-06-05 09:43:29 [http-nio-8081-exec-8] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:29 [http-nio-8081-exec-3] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-06-05 09:43:29 [http-nio-8081-exec-8] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-06-05 09:43:29 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.school.exception.GlobalExceptionHandler#handlerException(Exception)
2025-06-05 09:43:29 [http-nio-8081-exec-9] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:29 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.school.exception.GlobalExceptionHandler#handlerException(Exception)
2025-06-05 09:43:29 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:29 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json;q=0.8', given [image/avif, image/webp, image/apng, image/svg+xml, image/*, */*;q=0.8] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:29 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 500, "msg": "出现问题，请寻求管理员帮助", "data": null}]
2025-06-05 09:43:29 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg.]
2025-06-05 09:43:29 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:29 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json;q=0.8', given [image/avif, image/webp, image/apng, image/svg+xml, image/*, */*;q=0.8] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:29 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 500, "msg": "出现问题，请寻求管理员帮助", "data": null}]
2025-06-05 09:43:29 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg.]
2025-06-05 09:43:29 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:29 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:29 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:29 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg", parameters={}
2025-06-05 09:43:29 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:29 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.school.exception.GlobalExceptionHandler#handlerException(Exception)
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json;q=0.8', given [image/avif, image/webp, image/apng, image/svg+xml, image/*, */*;q=0.8] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:29 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 500, "msg": "出现问题，请寻求管理员帮助", "data": null}]
2025-06-05 09:43:30 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg.]
2025-06-05 09:43:30 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:30 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:32 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /posts/3/comments?page=1&size=10, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"7z9Rj2oOLRaeEq3u4qV64", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:32 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - GET "/posts/3/comments?page=1&size=10", parameters={masked}
2025-06-05 09:43:32 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.social.PostController#getComments(Long, Integer, Integer)
2025-06-05 09:43:32 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /posts/3, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"XhBFBoqAWyKE1tLSzKpIZ", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:32 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/posts/3", parameters={}
2025-06-05 09:43:32 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.social.PostController#getPostDetail(Long)
2025-06-05 09:43:32 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.social.PostController.getPostDetail
2025-06-05 09:43:32 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.social.PostController.getComments
2025-06-05 09:43:32 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [3,1,10]
2025-06-05 09:43:32 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [3]
2025-06-05 09:43:32 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:32 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:32 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:32 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:32 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:32 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:32 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:32 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG c.s.m.PostMapper.incrementViewCount - ==>  Preparing: UPDATE posts SET view_count = view_count + 1 WHERE post_id = ?
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG c.s.m.PostMapper.incrementViewCount - ==> Parameters: 3(Long)
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG c.s.m.C.selectCommentListWithUser - ==>  Preparing: SELECT c.comment_id as id, c.post_id as postId, c.user_id as userId, u.name as userName, u.avatar as userAvatar, c.content, c.parent_id as parentId, c.reply_to_user_id as replyToUserId, ru.name as replyToUserName, c.like_count as likeCount, c.created_time as createdTime, CASE WHEN l.like_id IS NOT NULL THEN 1 ELSE 0 END as isLiked FROM comments c LEFT JOIN users u ON c.user_id = u.user_id LEFT JOIN users ru ON c.reply_to_user_id = ru.user_id LEFT JOIN likes l ON l.target_id = c.comment_id AND l.target_type = 'comment' AND l.user_id = ? WHERE c.post_id = ? AND c.status = 1 ORDER BY c.created_time ASC LIMIT ?, ?
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG c.s.m.C.selectCommentListWithUser - ==> Parameters: 1(Integer), 3(Long), 0(Integer), 10(Integer)
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG c.s.m.C.selectCommentListWithUser - <==      Total: 0
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG c.s.m.C.selectCommentCount - ==>  Preparing: SELECT COUNT(*) FROM comments WHERE post_id = ? AND status = 1
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG c.s.m.C.selectCommentCount - ==> Parameters: 3(Long)
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG c.s.m.C.selectCommentCount - <==      Total: 1
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.social.PostController.getComments, 返回结果: {"code":200,"msg":"评论列表查询成功","data":{"total":0,"current":1,"pages":0,"size":10,"records":[]}}
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG c.s.m.PostMapper.incrementViewCount - <==    Updates: 1
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG c.s.m.P.selectPostDetailWithUser - ==>  Preparing: SELECT p.post_id as id, p.user_id as userId, u.name as userName, u.avatar as userAvatar, p.content, p.images, p.skill_id as skillId, ss.title as skillTitle, p.like_count as likeCount, p.comment_count as commentCount, p.view_count as viewCount, p.created_time as createdTime, CASE WHEN l.like_id IS NOT NULL THEN 1 ELSE 0 END as isLiked, CASE WHEN uf.id IS NOT NULL THEN 1 ELSE 0 END as isFollowed FROM posts p LEFT JOIN users u ON p.user_id = u.user_id LEFT JOIN skill_services ss ON p.skill_id = ss.service_id LEFT JOIN likes l ON l.target_id = p.post_id AND l.target_type = 'post' AND l.user_id = ? LEFT JOIN user_follows uf ON uf.following_id = p.user_id AND uf.follower_id = ? WHERE p.post_id = ? AND p.status = 1
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG c.s.m.P.selectPostDetailWithUser - ==> Parameters: 1(Integer), 1(Integer), 3(Long)
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 社交(String), 查询(String), 查询动态评论(String), /posts/3/comments(String), GET(String), [3,1,10](String), {"x-request-id":"7z9Rj2oOLRaeEq3u4qV64","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 评论列表查询成功(String), {"total":0,"current":1,"pages":0,"size":10,"records":[]}(String), 1(Long), 测试账户二(String), 2025-06-05T09:43:33.017981800(LocalDateTime), 32(Long)
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG c.s.m.P.selectPostDetailWithUser - <==      Total: 1
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.social.PostController.getPostDetail, 返回结果: {"code":200,"msg":"动态详情查询成功","data":{"images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"],"isLiked":0,"createdTime":1749086702000,"likeCount":0,"id":3,"viewCount":1,"userName":"管理员","userId":2,"content":"123124","isFollowed":0,"commentCount":0}}
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 社交(String), 查询(String), 查询动态详情(String), /posts/3(String), GET(String), [3](String), {"x-request-id":"XhBFBoqAWyKE1tLSzKpIZ","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 动态详情查询成功(String), {"images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"],"isLiked":0,"createdTime":1749086702000,"likeCount":0,"id":3,"viewCount":1,"userName":"管理员","userId":2,"content":"123124","isFollowed":0,"commentCount":0}(String), 1(Long), 测试账户二(String), 2025-06-05T09:43:33.023147400(LocalDateTime), 37(Long)
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "评论列表查询成功", "data": {total=0, current=1, pages=0, size=10, records=[]}}]
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:33 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /posts/3/comments?page=1&size=10, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"7z9Rj2oOLRaeEq3u4qV64", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "动态详情查询成功", "data": {images=[/files/socialpost/0b3832af6f9a4243a082afce902f11f7 (truncated)...]
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:33 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /posts/3, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"XhBFBoqAWyKE1tLSzKpIZ", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:33 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:33 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:33 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:33 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:33 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg", parameters={}
2025-06-05 09:43:33 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg", parameters={}
2025-06-05 09:43:33 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp", parameters={}
2025-06-05 09:43:33 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg", parameters={}
2025-06-05 09:43:33 [http-nio-8081-exec-2] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:33 [http-nio-8081-exec-8] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:33 [http-nio-8081-exec-9] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:33 [http-nio-8081-exec-10] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:33 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:33 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:33 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:33 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:33 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:33 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:33 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:33 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [POST /users/2/follow, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"15", pragma:"no-cache", cache-control:"no-cache", x-request-id:"Qzfu1IZGeRdQPaOW5kkSd", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", Content-Type:"application/json;charset=UTF-8"]]
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - POST "/users/2/follow", parameters={}
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.social.UserFollowController#followUser(Integer, Map)
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{follow=true}]
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.social.UserFollowController.followUser
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [2,{"follow":true}]
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.s.m.U.selectByFollowerAndFollowing - ==>  Preparing: SELECT * FROM user_follows WHERE follower_id = ? AND following_id = ?
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.s.m.U.selectByFollowerAndFollowing - ==> Parameters: 1(Integer), 2(Integer)
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.s.m.U.selectByFollowerAndFollowing - <==      Total: 0
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserFollowMapper.insert - ==>  Preparing: INSERT INTO user_follows ( follower_id, following_id, created_time ) VALUES ( ?, ?, ? )
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserFollowMapper.insert - ==> Parameters: 1(Integer), 2(Integer), 2025-06-05 09:43:39.492(Timestamp)
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserFollowMapper.insert - <==    Updates: 1
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.social.UserFollowController.followUser, 返回结果: {"code":200,"msg":"关注成功","data":null}
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 社交(String), 关注(String), 关注/取消关注用户(String), /users/2/follow(String), POST(String), [2,{"follow":true}](String), {"x-request-id":"Qzfu1IZGeRdQPaOW5kkSd","sec-fetch-mode":"cors","content-length":"15","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","content-type":"application/json","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 关注成功(String), null(String), 1(Long), 测试账户二(String), 2025-06-05T09:43:39.516104100(LocalDateTime), 48(Long)
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "关注成功", "data": null}]
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:39 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: POST /users/2/follow, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"15", pragma:"no-cache", cache-control:"no-cache", x-request-id:"Qzfu1IZGeRdQPaOW5kkSd", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", Content-Type:"application/json;charset=UTF-8"], payload={"follow":true}]
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"Q_t0x3LBQuGlVG1uw6oTl", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /posts/list?page=1&pageSize=10&followed=false, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"8N6CmSj0aSse0dfH31ojS", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/posts/list?page=1&pageSize=10&followed=false", parameters={masked}
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.social.PostController#getPostList(Integer, Integer, Integer, String, Boolean)
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /social/users/recommended?limit=5, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"ifU-9ZOTgmFAcmlI0Bbkj", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /social/posts/hot?limit=5, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"EA60_3rff8cj1St2kkR37", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - GET "/social/users/recommended?limit=5", parameters={masked}
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/social/posts/hot?limit=5", parameters={masked}
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.social.SocialController#getRecommendedUsers(Integer)
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.social.SocialController#getHotPosts(Integer)
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.social.PostController.getPostList
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [1,10,null,null,false]
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.social.SocialController.getRecommendedUsers
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.social.SocialController.getHotPosts
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [5]
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 方法参数: [5]
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.s.m.P.selectPostListWithUser - ==>  Preparing: SELECT p.post_id as id, p.user_id as userId, u.name as userName, u.avatar as userAvatar, p.content, p.images, p.skill_id as skillId, ss.title as skillTitle, p.like_count as likeCount, p.comment_count as commentCount, p.view_count as viewCount, p.created_time as createdTime, CASE WHEN l.like_id IS NOT NULL THEN 1 ELSE 0 END as isLiked, CASE WHEN uf.id IS NOT NULL THEN 1 ELSE 0 END as isFollowed FROM posts p LEFT JOIN users u ON p.user_id = u.user_id LEFT JOIN skill_services ss ON p.skill_id = ss.service_id LEFT JOIN likes l ON l.target_id = p.post_id AND l.target_type = 'post' AND l.user_id = ? LEFT JOIN user_follows uf ON uf.following_id = p.user_id AND uf.follower_id = ? WHERE p.status = 1 ORDER BY p.created_time DESC LIMIT ?, ?
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.s.m.P.selectPostListWithUser - ==> Parameters: 1(Integer), 1(Integer), 0(Integer), 10(Integer)
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG c.s.m.U.selectRecommendedUsers - ==>  Preparing: SELECT u.user_id as userId, u.name as userName, u.avatar as userAvatar, u.user_type as userType, COALESCE(follower_counts.followers_count, 0) as followersCount, CASE WHEN uf.id IS NOT NULL THEN 1 ELSE 0 END as isFollowed FROM users u LEFT JOIN ( SELECT following_id, COUNT(*) as followers_count FROM user_follows GROUP BY following_id ) follower_counts ON u.user_id = follower_counts.following_id LEFT JOIN user_follows uf ON uf.follower_id = ? AND uf.following_id = u.user_id WHERE u.user_id != ? AND u.status = 1 ORDER BY follower_counts.followers_count DESC, u.user_id ASC LIMIT ?
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG c.s.m.U.selectRecommendedUsers - ==> Parameters: 1(Integer), 1(Integer), 5(Integer)
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG c.s.mapper.PostMapper.selectHotPosts - ==>  Preparing: SELECT p.post_id as id, p.user_id as userId, u.name as userName, u.avatar as userAvatar, u.user_type as userType, p.content, p.images, p.skill_id as skillId, ss.title as skillTitle, p.like_count as likeCount, p.comment_count as commentCount, p.view_count as viewCount, p.created_time as createdTime, CASE WHEN l.like_id IS NOT NULL THEN 1 ELSE 0 END as isLiked, CASE WHEN uf.id IS NOT NULL THEN 1 ELSE 0 END as isFollowed FROM posts p LEFT JOIN users u ON p.user_id = u.user_id LEFT JOIN skill_services ss ON p.skill_id = ss.service_id LEFT JOIN likes l ON l.target_id = p.post_id AND l.target_type = 'post' AND l.user_id = ? LEFT JOIN user_follows uf ON uf.following_id = p.user_id AND uf.follower_id = ? WHERE p.status = 1 ORDER BY p.like_count DESC, p.created_time DESC LIMIT ?
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG c.s.mapper.PostMapper.selectHotPosts - ==> Parameters: 1(Integer), 1(Integer), 5(Integer)
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG c.s.m.U.selectRecommendedUsers - <==      Total: 1
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.social.SocialController.getRecommendedUsers, 返回结果: {"code":200,"msg":"查询成功","data":[{"userType":1,"followersCount":1,"userName":"测试账户一","userId":3,"isFollowed":1}]}
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.s.m.P.selectPostListWithUser - <==      Total: 3
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.s.m.PostMapper.selectPostCount - ==>  Preparing: SELECT COUNT(*) FROM posts p WHERE p.status = 1
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG c.s.mapper.PostMapper.selectHotPosts - <==      Total: 3
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.s.m.PostMapper.selectPostCount - ==> Parameters: 
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 1(String)
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.social.SocialController.getHotPosts, 返回结果: {"code":200,"msg":"查询成功","data":[{"images":["/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg"],"isLiked":1,"createdTime":1749025083000,"likeCount":1,"id":2,"userType":1,"viewCount":31,"userName":"测试账户二","userId":1,"content":"# 权限架构是什么\n权限架构（Permission Architecture）是一种管理和控制系统中用户访问资源的方式。它定义了谁可以访问哪些资源以及如何进行这些访问。权限架构的主要目标是确保系统的安全性和数据的完整性，防止未授权的访问和潜在的安全威胁。\n通俗一点说，对于一个系统，我们需要规定每个用户能干什么，不能干什么，这也就是每个用户的权限，我们需要设置合理的权限去管理每一个接口。\n# 权限架构包含什么\n一个最基础的权限架构通常会包含三个内容，两种，一个是具体的用户，一个是用户组，一个是权限，另一种是，用户，角色，权限。在大部分的系统中，我们通常只会将权限赋予用户组，然后用户属于用户组。\n# 五表结构\n经典的五表结构是指在权限管理系统中常见的五张核心数据表，用于管理用户、角色、权限以及它们之间的关系。这种结构能够清晰地定义和维护权限体系，广泛应用于基于角色的访问控制（RBAC）模型。五表结构通常包括以下五张表：\n## 用户表（users）\n- 记录系统中所有用户的信息。\n- 典型字段：用户ID、用户名、密码、电子邮件、状态（如激活或禁用）等。\n```\nCREATE TABLE users (\n  user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',\n  username VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '用户名',\n  PASSWORD VARCHAR ( 255 ) NOT NULL COMMENT '密码',\n  email VARCHAR ( 255 ) NOT NULL COMMENT '邮箱，用于登录',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '用户表';\n```\n## 角色表（Role）\n- 存储角色的基本信息，如角色ID、角色名、角色描述等。角色是权限的集合，用户通过角色来获得权限。\n```\nCREATE TABLE roles (\n  role_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',\n  role_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '角色名称',\n  description VARCHAR ( 255 ) COMMENT '角色描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '角色表';\n```\n## 权限表（Permission）\n- 存储权限的基本信息，如权限ID、权限名、权限描述等。权限通常对应于系统中的某些操作或资源的访问控制。\n```\nCREATE TABLE permissions (\n  permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',\n  permission_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '权限名称',\n  description VARCHAR ( 255 ) COMMENT '权限描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '权限表';\n```\n## 用户角色关系表（UserRole)：\n- 存储用户和角色之间的关系，通常包括用户ID和角色ID。这个表用来表示一个用户拥有哪些角色。\n```\nCREATE TABLE user_roles (\n  user_id INT COMMENT '用户ID',\n  role_id INT COMMENT '角色ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( user_id, role_id ),\n  FOREIGN KEY ( user_id ) REFERENCES users ( user_id ) ON DELETE CASCADE ON UPDATE CASCADE,\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '用户角色关联表';\n```\n## 角色权限关联表（rolePermissions）\n- 存储权限与角色之间的关系，通常包括权限ID和角色ID。这个表用来表示一个角色拥有哪些权限\n```\nCREATE TABLE role_permissions (\n  role_id INT COMMENT '角色ID',\n  permission_id INT COMMENT '权限ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( role_id, permission_id ),\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE,\nFOREIGN KEY ( permission_id ) REFERENCES permissions ( permission_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '角色权限关联表';\n```\n","isFollowed":0,"commentCount":1},{"images":["/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg","/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg"],"isLiked":1,"createdTime":1749022456000,"likeCount":1,"id":1,"userType":1,"viewCount":26,"userName":"测试账户一","userId":3,"content":"·12412412","isFollowed":1,"commentCount":3},{"images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"],"isLiked":0,"createdTime":1749086702000,"likeCount":0,"id":3,"userType":1,"viewCount":1,"userName":"管理员","userId":2,"content":"123124","isFollowed":1,"commentCount":0}]}
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 社交管理(String), 查看推荐用户(String), 获取推荐用户列表(String), /social/users/recommended(String), GET(String), [5](String), {"x-request-id":"ifU-9ZOTgmFAcmlI0Bbkj","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 查询成功(String), [{"userType":1,"followersCount":1,"userName":"测试账户一","userId":3,"isFollowed":1}](String), 1(Long), 测试账户二(String), 2025-06-05T09:43:44.368416300(LocalDateTime), 19(Long)
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 社交管理(String), 查看热门帖子(String), 获取热门帖子列表(String), /social/posts/hot(String), GET(String), [5](String), {"x-request-id":"EA60_3rff8cj1St2kkR37","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 查询成功(String), [{"images":["/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg"],"isLiked":1,"createdTime":1749025083000,"likeCount":1,"id":2,"userType":1,"viewCount":31,"userName":"测试账户二","userId":1,"content":"# 权限架构是什么\n权限架构（Permission Architecture）是一种管理和控制系统中用户访问资源的方式。它定义了谁可以访问哪些资源以及如何进行这些访问。权限架构的主要目标是确保系统的安全性和数据的完整性，防止未授权的访问和潜在的安全威胁。\n通俗一点说，对于一个系统，我们需要规定每个用户能干什么，不能干什么，这也就是每个用户的权限，我们需要设置合理的权限去管理每一个接口。\n# 权限架构包含什么\n一个最基础的权限架构通常会包含三个内容，两种，一个是具体的用户，一个是用户组，一个是权限，另一种是，用户，角色，权限。在大部分的系统中，我们通常只会将权限赋予用户组，然后用户属于用户组。\n# 五表结构\n经典的五表结构是指在权限管理系统中常见的五张核心数据表，用于管理用户、角色、权限以及它们之间的关系。这种结构能够清晰地定义和维护权限体系，广泛应用于基于角色的访问控制（RBAC）模型。五表结构通常包括以下五张表：\n## 用户表（users）\n- 记录系统中所有用户的信息。\n- 典型字段：用户ID、用户名、密码、电子邮件、状态（如激活或禁用）等。\n```\nCREATE TABLE users (\n  user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',\n  username VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '用户名',\n  PASSWORD VARCHAR ( 255 ) NOT NULL COMMENT '密码',\n  email VARCHAR ( 255 ) NOT NULL COMMENT '邮箱，用于登录',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '用户表';\n```\n## 角色表（Role）\n- 存储角色的基本信息，如角色ID、角色名、角色描述等。角色是权限的集合，用户通过角色来获得权限。\n```\nCREATE TABLE roles (\n  role_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',\n  role_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '角色名称',\n  description VARCHAR ( 255 ) COMMENT '角色描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '角色表';\n```\n## 权限表（Permission）\n- 存储权限的基本信息，如权限ID、权限名、权限描述等。权限通常对应于系统中的某些操作或资源的访问控制。\n```\nCREATE TABLE permissions (\n  permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',\n  permission_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '权限名称',\n  description VARCHAR ( 255 ) COMMENT '权限描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '权限表';\n```\n## 用户角色关系表（UserRole)：\n- 存储用户和角色之间的关系，通常包括用户ID和角色ID。这个表用来表示一个用户拥有哪些角色。\n```\nCREATE TABLE user_roles (\n  user_id INT COMMENT '用户ID',\n  role_id INT COMMENT '角色ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( user_id, role_id ),\n  FOREIGN KEY ( user_id ) REFERENCES users ( user_id ) ON DELETE CASCADE ON UPDATE CASCADE,\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '用户角色关联表';\n```\n## 角色权限关联表（rolePermissions）\n- 存储权限与角色之间的关系，通常包括权限ID和角色ID。这个表用来表示一个角色拥有哪些权限\n```\nCREATE TABLE role_permissions (\n  role_id INT COMMENT '角色ID',\n  permission_id INT COMMENT '权限ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( role_id, permission_id ),\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE,\nFOREIGN KEY ( permission_id ) REFERENCES permissions ( permission_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '角色权限关联表';\n```\n","isFollowed":0,"commentCount":1},{"images":["/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg","/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg"],"isLiked":1,"createdTime":1749022456000,"likeCount":1,"id":1,"userType":1,"viewCount":26,"userName":"测试账户一","userId":3,"content":"·12412412","isFollowed":1,"commentCount":3},{"images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"],"isLiked":0,"createdTime":1749086702000,"likeCount":0,"id":3,"userType":1,"viewCount":1,"userName":"管理员","userId":2,"content":"123124","isFollowed":1,"commentCount":0}](String), 1(Long), 测试账户二(String), 2025-06-05T09:43:44.370207900(LocalDateTime), 21(Long)
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}}
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.s.m.PostMapper.selectPostCount - <==      Total: 1
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.social.PostController.getPostList, 返回结果: {"code":200,"msg":"动态列表查询成功","data":{"total":3,"current":1,"pages":1,"size":10,"records":[{"images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"],"isLiked":0,"createdTime":1749086702000,"likeCount":0,"id":3,"viewCount":1,"userName":"管理员","userId":2,"content":"123124","isFollowed":1,"commentCount":0},{"images":["/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg"],"isLiked":1,"createdTime":1749025083000,"likeCount":1,"id":2,"viewCount":31,"userName":"测试账户二","userId":1,"content":"# 权限架构是什么\n权限架构（Permission Architecture）是一种管理和控制系统中用户访问资源的方式。它定义了谁可以访问哪些资源以及如何进行这些访问。权限架构的主要目标是确保系统的安全性和数据的完整性，防止未授权的访问和潜在的安全威胁。\n通俗一点说，对于一个系统，我们需要规定每个用户能干什么，不能干什么，这也就是每个用户的权限，我们需要设置合理的权限去管理每一个接口。\n# 权限架构包含什么\n一个最基础的权限架构通常会包含三个内容，两种，一个是具体的用户，一个是用户组，一个是权限，另一种是，用户，角色，权限。在大部分的系统中，我们通常只会将权限赋予用户组，然后用户属于用户组。\n# 五表结构\n经典的五表结构是指在权限管理系统中常见的五张核心数据表，用于管理用户、角色、权限以及它们之间的关系。这种结构能够清晰地定义和维护权限体系，广泛应用于基于角色的访问控制（RBAC）模型。五表结构通常包括以下五张表：\n## 用户表（users）\n- 记录系统中所有用户的信息。\n- 典型字段：用户ID、用户名、密码、电子邮件、状态（如激活或禁用）等。\n```\nCREATE TABLE users (\n  user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',\n  username VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '用户名',\n  PASSWORD VARCHAR ( 255 ) NOT NULL COMMENT '密码',\n  email VARCHAR ( 255 ) NOT NULL COMMENT '邮箱，用于登录',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '用户表';\n```\n## 角色表（Role）\n- 存储角色的基本信息，如角色ID、角色名、角色描述等。角色是权限的集合，用户通过角色来获得权限。\n```\nCREATE TABLE roles (\n  role_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',\n  role_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '角色名称',\n  description VARCHAR ( 255 ) COMMENT '角色描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '角色表';\n```\n## 权限表（Permission）\n- 存储权限的基本信息，如权限ID、权限名、权限描述等。权限通常对应于系统中的某些操作或资源的访问控制。\n```\nCREATE TABLE permissions (\n  permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',\n  permission_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '权限名称',\n  description VARCHAR ( 255 ) COMMENT '权限描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '权限表';\n```\n## 用户角色关系表（UserRole)：\n- 存储用户和角色之间的关系，通常包括用户ID和角色ID。这个表用来表示一个用户拥有哪些角色。\n```\nCREATE TABLE user_roles (\n  user_id INT COMMENT '用户ID',\n  role_id INT COMMENT '角色ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( user_id, role_id ),\n  FOREIGN KEY ( user_id ) REFERENCES users ( user_id ) ON DELETE CASCADE ON UPDATE CASCADE,\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '用户角色关联表';\n```\n## 角色权限关联表（rolePermissions）\n- 存储权限与角色之间的关系，通常包括权限ID和角色ID。这个表用来表示一个角色拥有哪些权限\n```\nCREATE TABLE role_permissions (\n  role_id INT COMMENT '角色ID',\n  permission_id INT COMMENT '权限ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( role_id, permission_id ),\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE,\nFOREIGN KEY ( permission_id ) REFERENCES permissions ( permission_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '角色权限关联表';\n```\n","isFollowed":0,"commentCount":1},{"images":["/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg","/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg"],"isLiked":1,"createdTime":1749022456000,"likeCount":1,"id":1,"viewCount":26,"userName":"测试账户一","userId":3,"content":"·12412412","isFollowed":1,"commentCount":3}]}}
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"Q_t0x3LBQuGlVG1uw6oTl","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}(String), 1(Long), 测试账户二(String), 2025-06-05T09:43:44.372665200(LocalDateTime), 26(Long)
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 社交(String), 查询(String), 查询动态列表(String), /posts/list(String), GET(String), [1,10,null,null,false](String), {"x-request-id":"8N6CmSj0aSse0dfH31ojS","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 动态列表查询成功(String), {"total":3,"current":1,"pages":1,"size":10,"records":[{"images":["/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg","/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg","/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg","/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp"],"isLiked":0,"createdTime":1749086702000,"likeCount":0,"id":3,"viewCount":1,"userName":"管理员","userId":2,"content":"123124","isFollowed":1,"commentCount":0},{"images":["/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg"],"isLiked":1,"createdTime":1749025083000,"likeCount":1,"id":2,"viewCount":31,"userName":"测试账户二","userId":1,"content":"# 权限架构是什么\n权限架构（Permission Architecture）是一种管理和控制系统中用户访问资源的方式。它定义了谁可以访问哪些资源以及如何进行这些访问。权限架构的主要目标是确保系统的安全性和数据的完整性，防止未授权的访问和潜在的安全威胁。\n通俗一点说，对于一个系统，我们需要规定每个用户能干什么，不能干什么，这也就是每个用户的权限，我们需要设置合理的权限去管理每一个接口。\n# 权限架构包含什么\n一个最基础的权限架构通常会包含三个内容，两种，一个是具体的用户，一个是用户组，一个是权限，另一种是，用户，角色，权限。在大部分的系统中，我们通常只会将权限赋予用户组，然后用户属于用户组。\n# 五表结构\n经典的五表结构是指在权限管理系统中常见的五张核心数据表，用于管理用户、角色、权限以及它们之间的关系。这种结构能够清晰地定义和维护权限体系，广泛应用于基于角色的访问控制（RBAC）模型。五表结构通常包括以下五张表：\n## 用户表（users）\n- 记录系统中所有用户的信息。\n- 典型字段：用户ID、用户名、密码、电子邮件、状态（如激活或禁用）等。\n```\nCREATE TABLE users (\n  user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',\n  username VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '用户名',\n  PASSWORD VARCHAR ( 255 ) NOT NULL COMMENT '密码',\n  email VARCHAR ( 255 ) NOT NULL COMMENT '邮箱，用于登录',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '用户表';\n```\n## 角色表（Role）\n- 存储角色的基本信息，如角色ID、角色名、角色描述等。角色是权限的集合，用户通过角色来获得权限。\n```\nCREATE TABLE roles (\n  role_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',\n  role_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '角色名称',\n  description VARCHAR ( 255 ) COMMENT '角色描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '角色表';\n```\n## 权限表（Permission）\n- 存储权限的基本信息，如权限ID、权限名、权限描述等。权限通常对应于系统中的某些操作或资源的访问控制。\n```\nCREATE TABLE permissions (\n  permission_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',\n  permission_name VARCHAR ( 50 ) NOT NULL UNIQUE COMMENT '权限名称',\n  description VARCHAR ( 255 ) COMMENT '权限描述',\n  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' \n) COMMENT '权限表';\n```\n## 用户角色关系表（UserRole)：\n- 存储用户和角色之间的关系，通常包括用户ID和角色ID。这个表用来表示一个用户拥有哪些角色。\n```\nCREATE TABLE user_roles (\n  user_id INT COMMENT '用户ID',\n  role_id INT COMMENT '角色ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( user_id, role_id ),\n  FOREIGN KEY ( user_id ) REFERENCES users ( user_id ) ON DELETE CASCADE ON UPDATE CASCADE,\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '用户角色关联表';\n```\n## 角色权限关联表（rolePermissions）\n- 存储权限与角色之间的关系，通常包括权限ID和角色ID。这个表用来表示一个角色拥有哪些权限\n```\nCREATE TABLE role_permissions (\n  role_id INT COMMENT '角色ID',\n  permission_id INT COMMENT '权限ID',\n  assigned_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',\n  PRIMARY KEY ( role_id, permission_id ),\n  FOREIGN KEY ( role_id ) REFERENCES roles ( role_id ) ON DELETE CASCADE ON UPDATE CASCADE,\nFOREIGN KEY ( permission_id ) REFERENCES permissions ( permission_id ) ON DELETE CASCADE ON UPDATE CASCADE \n) COMMENT '角色权限关联表';\n```\n","isFollowed":0,"commentCount":1},{"images":["/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg","/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg"],"isLiked":1,"createdTime":1749022456000,"likeCount":1,"id":1,"viewCount":26,"userName":"测试账户一","userId":3,"content":"·12412412","isFollowed":1,"commentCount":3}]}(String), 1(Long), 测试账户二(String), 2025-06-05T09:43:44.373924600(LocalDateTime), 27(Long)
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "查询成功", "data": [{userType=1, followersCount=1, userName=测试账户一, userId=3, isFol (truncated)...]
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /social/users/recommended?limit=5, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"ifU-9ZOTgmFAcmlI0Bbkj", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "动态列表查询成功", "data": {total=3, current=1, pages=1, size=10, records=[{images=[/f (truncated)...]
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "查询成功", "data": [{images=[/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d202 (truncated)...]
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=1, name=测试账户二, stuId=220911123, academy=1111, e (truncated)...]
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:44 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"Q_t0x3LBQuGlVG1uw6oTl", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:44 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /social/posts/hot?limit=5, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"EA60_3rff8cj1St2kkR37", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /posts/list?page=1&pageSize=10&followed=false, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"8N6CmSj0aSse0dfH31ojS", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-1] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg", parameters={}
2025-06-05 09:43:44 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg", parameters={}
2025-06-05 09:43:44 [http-nio-8081-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp", parameters={}
2025-06-05 09:43:44 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg", parameters={}
2025-06-05 09:43:44 [http-nio-8081-exec-1] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:44 [http-nio-8081-exec-4] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:44 [http-nio-8081-exec-5] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:44 [http-nio-8081-exec-7] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:44 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:44 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/0b3832af6f9a4243a082afce902f11f720250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:44 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/4499742b7118468dbd223b8fe87e8c7020250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg", parameters={}
2025-06-05 09:43:44 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg", parameters={}
2025-06-05 09:43:44 [http-nio-8081-exec-6] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:44 [http-nio-8081-exec-3] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:44 [http-nio-8081-exec-6] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-06-05 09:43:44 [http-nio-8081-exec-3] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-06-05 09:43:44 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:44 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/343554dfeb2744d3ba791e532a961e0120250605092457.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg", parameters={}
2025-06-05 09:43:44 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.school.exception.GlobalExceptionHandler#handlerException(Exception)
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-06-05 09:43:44 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.school.exception.GlobalExceptionHandler#handlerException(Exception)
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.school.exception.GlobalExceptionHandler#handlerException(Exception)
2025-06-05 09:43:44 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json;q=0.8', given [image/avif, image/webp, image/apng, image/svg+xml, image/*, */*;q=0.8] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:44 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 500, "msg": "出现问题，请寻求管理员帮助", "data": null}]
2025-06-05 09:43:44 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg.]
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json;q=0.8', given [image/avif, image/webp, image/apng, image/svg+xml, image/*, */*;q=0.8] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:44 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 500, "msg": "出现问题，请寻求管理员帮助", "data": null}]
2025-06-05 09:43:44 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/c6fa17dd4aad40de897a73f90b43b08b20250604153403.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg.]
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:44 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/8650ccf52eb44b1d9a77f26c32d8d1db20250604153409.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json;q=0.8', given [image/avif, image/webp, image/apng, image/svg+xml, image/*, */*;q=0.8] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:43:44 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 500, "msg": "出现问题，请寻求管理员帮助", "data": null}]
2025-06-05 09:43:44 [http-nio-8081-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:44 [http-nio-8081-exec-1] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/ac99c74493954cfca6f2b354269460b920250605092457.webp, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:43:44 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg.]
2025-06-05 09:43:44 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:43:44 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/socialpost/13ecb3757a3e4957a6bbe4129abe3c8d20250604161801.jpg, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua-platform:""Windows"", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", accept:"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8", sec-fetch-site:"cross-site", sec-fetch-mode:"no-cors", sec-fetch-dest:"image", sec-fetch-storage-access:"active", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"IJq8t1H44dI-8y_VxZzjz", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"0mqBP5CgNKASDOjK8NB3b", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 1(String)
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}}
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"IJq8t1H44dI-8y_VxZzjz","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}(String), 1(Long), 测试账户二(String), 2025-06-05T09:51:48.565938900(LocalDateTime), 15(Long)
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 1(String)
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}}
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"0mqBP5CgNKASDOjK8NB3b","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}(String), 1(Long), 测试账户二(String), 2025-06-05T09:51:48.573654600(LocalDateTime), 12(Long)
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=1, name=测试账户二, stuId=220911123, academy=1111, e (truncated)...]
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:51:48 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"IJq8t1H44dI-8y_VxZzjz", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=1, name=测试账户二, stuId=220911123, academy=1111, e (truncated)...]
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:51:48 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"0mqBP5CgNKASDOjK8NB3b", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"jjTR10h6cz9Ru0AdxCBfN", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 1(String)
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}}
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"jjTR10h6cz9Ru0AdxCBfN","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}(String), 1(Long), 测试账户二(String), 2025-06-05T09:51:49.532275900(LocalDateTime), 16(Long)
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=1, name=测试账户二, stuId=220911123, academy=1111, e (truncated)...]
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:51:49 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"jjTR10h6cz9Ru0AdxCBfN", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"z4_GdVl177rPlylAdWza7", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 测试账户二, ID: 1
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 1(Integer)
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 1(String)
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}}
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"z4_GdVl177rPlylAdWza7","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"e7abbe46-eb8b-4c2c-9431-af3c4c82881b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":1,"name":"测试账户二","stuId":"220911123","academy":"1111","email":"<EMAIL>","avatar":null,"cardPhoto":"/files/card/33d9c7ae8fbd4ee4a684683f101affd220250525093544.jpg","cardId":"123","roles":["student"],"isfirstlogin":false}(String), 1(Long), 测试账户二(String), 2025-06-05T09:51:49.567861300(LocalDateTime), 13(Long)
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=1, name=测试账户二, stuId=220911123, academy=1111, e (truncated)...]
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:51:49 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"z4_GdVl177rPlylAdWza7", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:52:24 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /home/<USER>"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"gYx8blNmy15UuesHna03E", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:52:24 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/home/<USER>", parameters={}
2025-06-05 09:52:24 [http-nio-8081-exec-7] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/], classpath [static/], ServletContext [/]]
2025-06-05 09:52:24 [http-nio-8081-exec-7] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-06-05 09:52:24 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.school.exception.GlobalExceptionHandler#handlerException(Exception)
2025-06-05 09:52:24 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:52:24 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 500, "msg": "出现问题，请寻求管理员帮助", "data": null}]
2025-06-05 09:52:24 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource home/statistics.]
2025-06-05 09:52:24 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:52:24 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /home/<USER>"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"gYx8blNmy15UuesHna03E", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"e7abbe46-eb8b-4c2c-9431-af3c4c82881b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:54:49 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-05 09:54:49 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
